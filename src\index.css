
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --primary: 24 9.8% 10%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 60 4.8% 95.9%;
    --secondary-foreground: 24 9.8% 10%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 24 9.8% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 60 9.1% 97.8%;
    --card: 20 14.3% 4.1%;
    --card-foreground: 60 9.1% 97.8%;
    --popover: 20 14.3% 4.1%;
    --popover-foreground: 60 9.1% 97.8%;
    --primary: 60 9.1% 97.8%;
    --primary-foreground: 24 9.8% 10%;
    --secondary: 12 6.5% 15.1%;
    --secondary-foreground: 60 9.1% 97.8%;
    --muted: 12 6.5% 15.1%;
    --muted-foreground: 24 5.4% 63.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 60 9.1% 97.8%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 12 6.5% 15.1%;
    --input: 12 6.5% 15.1%;
    --ring: 24 5.7% 82.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  .text-shadow-sm {
    /* Text shadow removed */
  }

  .shadow-sm {
    /* Text shadow removed */
  }

  .heading-xl {
    @apply font-serif text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light tracking-tight;
  }

  .heading-lg {
    @apply font-serif text-2xl md:text-3xl lg:text-4xl font-light tracking-tight;
  }

  .heading-md {
    @apply font-serif text-xl md:text-2xl font-light;
  }

  .heading-sm {
    @apply font-serif text-lg md:text-xl font-light;
  }

  .body-lg {
    @apply text-base md:text-lg font-light;
  }

  .body-md {
    @apply text-sm md:text-base font-light;
  }

  .body-sm {
    @apply text-xs md:text-sm font-light;
  }

  .link-underline {
    @apply relative after:absolute after:bottom-0 after:left-0 after:h-px after:w-0 after:bg-current after:transition-all after:duration-300 hover:after:w-full;
  }

  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  .content-container {
    @apply max-w-7xl mx-auto px-6 md:px-10;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-white via-white to-champagne-300 bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply px-6 py-3 bg-primary text-primary-foreground hover:bg-primary/90 hover:text-white transition-colors relative overflow-hidden;
  }
.btn-primary::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%; /* Start off-screen to the left */
    width: 100%;
    height: 100%;
    background: linear-gradient(
      120deg,
      transparent,
      rgba(255, 255, 255, 0.4), /* Shine color */
      transparent
    );
    transition: left 0.6s ease-in-out; /* Slower, smoother transition */
  }

  .btn-primary:hover::before {
    left: 100%; /* Move across to the right on hover */
  }

  .animate-section-hidden {
    opacity: 0;
    transform: translateY(30px);
  }

  .animate-section-enter {
    animation: section-enter 0.8s ease forwards;
  }

  /* Advanced section reveal animations */
  .reveal-section-0 {
    animation: reveal-left 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  }

  .reveal-section-1 {
    animation: reveal-right 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  }

  .reveal-section-2 {
    animation: reveal-bottom 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  }

  /* New smooth transitions */
  .image-transition {
    transition: all 1s cubic-bezier(0.22, 1, 0.36, 1);
  }

  /* Advanced text reveal animation */
  .text-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1s ease, transform 1s ease;
  }

  .text-reveal.visible {
    opacity: 1;
    transform: translateY(0);
  }

  @keyframes section-enter {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes reveal-left {
    0% {
      opacity: 0;
      transform: translateX(-50px);
      clip-path: inset(0 100% 0 0);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
      clip-path: inset(0 0 0 0);
    }
  }

  @keyframes reveal-right {
    0% {
      opacity: 0;
      transform: translateX(50px);
      clip-path: inset(0 0 0 100%);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
      clip-path: inset(0 0 0 0);
    }
  }

  @keyframes reveal-bottom {
    0% {
      opacity: 0;
      transform: translateY(50px);
      clip-path: inset(100% 0 0 0);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
      clip-path: inset(0 0 0 0);
    }
  }

  /* Smoother subtle animations */
  @keyframes float-slow {
    0%, 100% {
      transform: translateY(0) translateX(0);
    }
    25% {
      transform: translateY(-15px) translateX(5px);
    }
    50% {
      transform: translateY(-20px) translateX(-5px);
    }
    75% {
      transform: translateY(-10px) translateX(-10px);
    }
  }

  @keyframes float-medium {
    0%, 100% {
      transform: translateY(0) translateX(0) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) translateX(5px) rotate(1deg);
    }
    66% {
      transform: translateY(-15px) translateX(-5px) rotate(-1deg);
    }
  }

  @keyframes float-fast {
    0%, 100% {
      transform: translateY(0) scale(1);
    }
    50% {
      transform: translateY(-8px) scale(1.03);
    }
  }

  @keyframes bounce-subtle {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-3px);
    }
  }

  /* Magnetic button effect */
  .magnetic-button {
    transition: transform 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  }

  /* Advanced ken burns effect */
  @keyframes ken-burns {
    0% {
      transform: scale(1.03) translateX(0) translateY(0);
    }
    25% {
      transform: scale(1.05) translateX(-5px) translateY(-2px);
    }
    50% {
      transform: scale(1.06) translateX(0) translateY(-5px);
    }
    75% {
      transform: scale(1.07) translateX(5px) translateY(-3px);
    }
    100% {
      transform: scale(1.08) translateX(0) translateY(0);
    }
  }

  /* Animated Button Styles */
  .animated-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 32px;
    border: none;
    font-size: 15px;
    background-color: #212121;
    color: white;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    min-width: 240px;
    height: 52px;
    margin: 24px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    text-align: center;
  }

  .animated-button svg {
    position: absolute;
    width: 18px;
    height: 18px;
    fill: white;
    z-index: 9;
    transition: all 0.3s ease;
  }

  .animated-button .arr-1 {
    right: 24px;
  }

  .animated-button .arr-2 {
    left: -30px;
    opacity: 0;
  }

  .animated-button .circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background-color: #a3e635; /* Lime-400 from Tailwind */
    border-radius: 50%;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  }

  .animated-button .text {
    position: relative;
    z-index: 1;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    padding: 0 10px;
    font-weight: 600;
    letter-spacing: 0.3px;
  }

  .animated-button:hover {
    color: #212121;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  }

  .animated-button:hover .arr-1 {
    right: -30px;
    opacity: 0;
  }

  .animated-button:hover .arr-2 {
    left: 24px;
    opacity: 1;
  }

  .animated-button:hover svg {
    fill: #212121;
  }

  .animated-button:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  }

  .animated-button:hover .circle {
    width: 400px;
    height: 400px;
    opacity: 1;
  }

  /* Green download button style */
  .download-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 36px;
    font-size: 15px;
    font-weight: 600;
    color: #000000;
    background-color: #a3e635; /* Lime-400 from Tailwind */
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 260px;
    height: 52px;
    text-align: center;
    margin: 24px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    letter-spacing: 0.3px;
  }

  .download-button:hover {
    background-color: #84cc16; /* Lime-500 from Tailwind */
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  }

  .download-button:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  }
}

/* Social Media Button Styles from Uiverse.io by roajuan93 */
.card {
  width: fit-content;
  height: fit-content;

  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25px 25px;
  gap: 20px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.055);
}

/* for all social containers*/
.socialContainer {
  width: 52px;
  height: 52px;
  border-radius: 5px;
  background-color: rgb(44, 44, 44);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition-duration: 0.3s;
}
/* instagram*/
.containerOne:hover {
  background-color: #d62976;
  transition-duration: 0.3s;
}
/* Tiktok*/
.containerTwo:hover {
  background-color: #25f4ee;
  transition-duration: 0.3s;
}
/* Facebook*/
.containerThree:hover {
  background-color: #1877f2;
  transition-duration: 0.3s;
}
/* Whatsapp*/
.containerFour:hover {
  background-color: green;
  transition-duration: 0.3s;
}

.socialContainer:active {
  transform: scale(0.9);
  transition-duration: 0.3s;
}

.socialSvg {
  width: 19px;
}
.largeIcon {
  width: 27px; /* Ancho específico solo para el icono de TikTok */
}
.socialSvg path {
  fill: rgb(255, 255, 255);
}

.socialContainer:hover .socialSvg {
  animation: slide-in-top 0.3s both;
}

@keyframes slide-in-top {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
