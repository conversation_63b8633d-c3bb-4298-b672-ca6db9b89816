import { useEffect, useRef, useState } from 'react';
import { useScroll, useTransform, useSpring } from 'framer-motion';

interface ScrollEffectsOptions {
  threshold?: number;
  rootMargin?: string;
  enableParallax?: boolean;
  parallaxSpeed?: number;
  enableFadeIn?: boolean;
  enableStagger?: boolean;
  staggerDelay?: number;
}

export const usePremiumScrollEffects = (options: ScrollEffectsOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '-50px',
    enableParallax = true,
    parallaxSpeed = 0.5,
    enableFadeIn = true,
    enableStagger = true,
    staggerDelay = 100
  } = options;

  const [inView, setInView] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef<HTMLElement>(null);
  
  // Framer Motion scroll progress
  const { scrollYProgress } = useScroll();
  
  // Smooth spring animations
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  // Parallax transforms
  const parallaxY = useTransform(
    smoothProgress,
    [0, 1],
    [0, enableParallax ? -100 * parallaxSpeed : 0]
  );

  const parallaxScale = useTransform(
    smoothProgress,
    [0, 1],
    [1, enableParallax ? 1 + (0.1 * parallaxSpeed) : 1]
  );

  // Intersection Observer for fade-in effects
  useEffect(() => {
    const element = ref.current;
    if (!element || !enableFadeIn) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setInView(true);
          setHasAnimated(true);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, enableFadeIn, hasAnimated]);

  // Premium easing curves
  const premiumEasing = {
    ease: [0.22, 1, 0.36, 1], // Custom cubic-bezier for premium feel
    duration: 0.8
  };

  const staggeredEasing = {
    ease: [0.25, 0.46, 0.45, 0.94],
    duration: 0.6
  };

  // Animation variants
  const fadeInVariants = {
    hidden: { 
      opacity: 0, 
      y: 60,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: premiumEasing
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: enableStagger ? staggerDelay / 1000 : 0,
        delayChildren: 0.1
      }
    }
  };

  const staggerItem = {
    hidden: { 
      opacity: 0, 
      y: 40,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: staggeredEasing
    }
  };

  // Magnetic effect for interactive elements
  const [magneticPosition, setMagneticPosition] = useState({ x: 0, y: 0 });

  const handleMagneticMove = (e: React.MouseEvent<HTMLElement>) => {
    const element = e.currentTarget;
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * 0.15;
    const deltaY = (e.clientY - centerY) * 0.15;
    
    setMagneticPosition({ x: deltaX, y: deltaY });
  };

  const handleMagneticLeave = () => {
    setMagneticPosition({ x: 0, y: 0 });
  };

  return {
    ref,
    inView,
    hasAnimated,
    parallaxY,
    parallaxScale,
    smoothProgress,
    fadeInVariants,
    staggerContainer,
    staggerItem,
    premiumEasing,
    magneticPosition,
    handleMagneticMove,
    handleMagneticLeave
  };
};

// Smooth scroll utility with premium easing
export const smoothScrollTo = (target: string | number, offset: number = 0) => {
  const targetElement = typeof target === 'string' 
    ? document.querySelector(target)
    : null;
  
  const targetPosition = targetElement 
    ? targetElement.getBoundingClientRect().top + window.pageYOffset - offset
    : typeof target === 'number' ? target : 0;

  window.scrollTo({
    top: targetPosition,
    behavior: 'smooth'
  });
};

// Premium page transition variants
export const pageTransitionVariants = {
  initial: {
    opacity: 0,
    scale: 0.98,
    y: 20
  },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.22, 1, 0.36, 1]
    }
  },
  exit: {
    opacity: 0,
    scale: 1.02,
    y: -20,
    transition: {
      duration: 0.4,
      ease: [0.22, 1, 0.36, 1]
    }
  }
};
