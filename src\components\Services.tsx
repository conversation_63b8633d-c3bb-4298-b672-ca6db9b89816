
import { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { usePremiumScrollEffects } from '@/hooks/usePremiumScrollEffects';

interface Package {
  name: string;
  price: string;
}

interface Service {
  id: string;
  title: string;
  description: string;
  image: string;
  features: string[];
  packages?: Package[];
}

const services: Service[] = [
  {
    id: 'prewedding',
    title: 'Pre Shoot Packages',
    description: 'Tailored photography experiences designed to preserve your most precious moments before the wedding day.',
    image: '/testimonials/Tharindu Dharmasena.jpg?v=2',
    features: [
      'Pre-shoot At Preferred Location',
      'Multiple Costume Options',
      'High End Edited Photos',
      'Drone Photos & Videos',
      'All Unedited Photos On Flash Drive'
    ],
    packages: [
      { name: 'Package 1', price: '30,000 LKR' },
      { name: 'Package 2', price: '50,000 LKR' },
      { name: 'Package 3', price: '80,000 LKR' }
    ]
  },
  {
    id: 'engagements',
    title: 'Engagement Packages',
    description: 'Celebrate your unique connection with a relaxed and romantic photoshoot that tells the story of your love before the big day.',
    image: '/testimonials/Yohani.jpg?v=2',
    features: [
      'Main Photo Session At Preferred Location',
      'Entire Ceremony & Reception Coverage',
      'Thank You Card Design For Social Media',
      'High End Edited Photos',
      'All Unedited Photos On Flash Drive'
    ],
    packages: [
      { name: 'Package 1', price: '35,000 LKR' },
      { name: 'Package 2', price: '55,000 LKR' },
      { name: 'Package 3', price: '75,000 LKR' }
    ]
  },
  {
    id: 'weddings',
    title: 'Wedding Packages',
    description: 'Documenting the magic of your wedding day, from the intimate preparations to the joyous celebrations, with artistry and care.',
    image: '/testimonials/Harsha.jpg?v=2',
    features: [
      'Main Photo Session At Preferred Location',
      'Entire Ceremony & Reception Coverage',
      'Story Albums & Enlarged Photos',
      'Thank You Cards',
      'High End Edited Photos',
      'All Unedited Photos On Flash Drive'
    ],
    packages: [
      { name: 'Package 1', price: '60,000 LKR' },
      { name: 'Package 2', price: '80,000 LKR' },
      { name: 'Package 3', price: '100,000 LKR' },
      { name: 'Package 4', price: '130,000 LKR' },
      { name: 'Package 5', price: '160,000 LKR' },
      { name: 'Package 6', price: '190,000 LKR' }
    ]
  },
  {
    id: 'homecoming',
    title: 'Home Coming Packages',
    description: 'Capturing the warmth and joy of your homecoming event as you celebrate the beginning of your life together with loved ones.',
    image: '/Pasindu & Nipuni/481338293_953059557025455_8367801125247642048_n.jpg',
    features: [
      'Main Photo Session At Preferred Location',
      'Entire Ceremony & Reception Coverage',
      'Thank You Card Design For Social Media',
      'High End Edited Photos',
      'All Unedited Photos On Flash Drive'
    ],
    packages: [
      { name: 'Package 1', price: '30,000 LKR' },
      { name: 'Package 2', price: '50,000 LKR' },
      { name: 'Package 3', price: '70,000 LKR' },
      { name: 'Package 4', price: '90,000 LKR' },
      { name: 'Package 5', price: '150,000 LKR' },
      { name: 'Package 6', price: '200,000 LKR' }
    ]
  },
  {
    id: 'dual',
    title: 'Wedding & Home Coming Packages',
    description: 'Comprehensive dual packages offering seamless coverage across wedding and homecoming events at preferential rates.',
    image: '/testimonials/Shanika.jpg?v=2',
    features: [
      'Wedding & Home Coming Coverage',
      'Main Photo Sessions At Preferred Locations',
      'Thank You Card Design For Social Media',
      'High End Edited Photos',
      'All Unedited Photos On Flash Drive'
    ],
    packages: [
      { name: 'Dual Package 1', price: '100,000 LKR' },
      { name: 'Dual Package 2', price: '160,000 LKR' },
      { name: 'Dual Package 3', price: '200,000 LKR' }
    ]
  },
  {
    id: 'combo',
    title: 'Pre Shoot, Wedding & Home Coming Packages',
    description: 'Complete combo packages covering all your photography needs from pre-shoot to wedding and homecoming celebrations.',
    image: '/Natasha & Ramitha/487313407_973486634982747_921204117200231749_n.jpg',
    features: [
      'Pre-shoot At Preferred Location',
      'Wedding & Home Coming Coverage',
      'Multiple Costume Options',
      'High End Edited Photos',
      'Drone Photos & Videos',
      'All Unedited Photos On Flash Drive'
    ],
    packages: [
      { name: 'Combo Package 1', price: '150,000 LKR' },
      { name: 'Combo Package 2', price: '200,000 LKR' },
      { name: 'Combo Package 3', price: '270,000 LKR' }
    ]
  }
];

const Services = () => {
  const [activeService, setActiveService] = useState(services[0].id);

  // Premium scroll effects
  const {
    ref: sectionRef,
    staggerContainer,
    staggerItem
  } = usePremiumScrollEffects({
    enableStagger: true,
    staggerDelay: 200
  });

  const currentService: Service = services.find(service => service.id === activeService) || services[0];

  return (
    <motion.section
      id="services"
      ref={sectionRef}
      className="section-padding bg-secondary"
      variants={staggerContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
    >
      <div className="content-container">
        <motion.div
          className="text-center mb-12 md:mb-20"
          variants={staggerItem}
        >
          <h2 className="heading-lg mb-5">Packages & Collections</h2>
          <p className="body-md max-w-xl mx-auto text-foreground/70 mb-8">
            Tailored photography experiences designed to preserve your most precious moments
          </p>
          <div className="flex justify-center mt-8 sm:mt-12">
            <a
              href="/Chalaka-Dulanga-Photography-Packages.pdf"
              download="Chalaka-Dulanga-Photography-Packages.pdf"
              className="animated-button text-xs sm:text-sm px-6 sm:px-8 min-w-[240px] sm:min-w-[240px] h-[52px]"
            >
              <svg viewBox="0 0 24 24" className="arr-2" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"
                ></path>
              </svg>
              <span className="text" style={{ textShadow: 'none' }}>Download Detailed Packages PDF</span>
              <span className="circle"></span>
              <svg viewBox="0 0 24 24" className="arr-1" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"
                ></path>
              </svg>
            </a>
          </div>
        </div>

        {/* Service tabs */}
        <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-12">
          {services.map(service => (
            <button
              type="button"
              key={service.id}
              onClick={() => setActiveService(service.id)}
              className={cn(
                "px-4 py-3 sm:px-6 sm:py-3 text-sm transition-all duration-300 font-serif",
                activeService === service.id
                  ? "bg-champagne-100 text-foreground"
                  : "bg-transparent text-foreground/70 hover:text-foreground"
              )}
            >
              <span style={{ textShadow: 'none' }}>{service.title}</span>
            </button>
          ))}
        </div>

        {/* Active service content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Service image */}
          <div className="overflow-hidden">
            {services.map(service => (
              <div
                key={service.id}
                className={cn(
                  "transition-all duration-700 ease-in-out",
                  activeService === service.id
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-8 hidden"
                )}
              >
                <div className="overflow-hidden aspect-[3/2]">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Service content */}
          <div className="lg:pl-8">
            <h3 className="heading-md mb-4" style={{ textShadow: 'none' }}>{currentService.title}</h3>
            <p className="body-md mb-8 text-foreground/80" style={{ textShadow: 'none' }}>
              {currentService.description}
            </p>

            <ul className="space-y-3">
              {currentService.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-3 mt-1 text-champagne-500">✦</span>
                  <span className="body-md" style={{ textShadow: 'none', fontWeight: 'normal' }}>{feature}</span>
                </li>
              ))}
            </ul>

            {/* Package pricing information */}
            {currentService.packages && (
              <div className="mt-8">
                <h4 className="font-serif text-lg mb-4" style={{ textShadow: 'none' }}>Package Options</h4>
                <ul className="space-y-2">
                  {currentService.packages.map((pkg, index) => (
                    <li key={index} className="flex justify-between items-center text-sm">
                      <span style={{ textShadow: 'none' }}>{pkg.name}</span>
                      <span className="font-medium" style={{ textShadow: 'none' }}>{pkg.price}</span>
                    </li>
                  ))}
                </ul>

              </div>
            )}

            <div className="mt-12 sm:mt-16 flex justify-center lg:justify-start">
              <a
                href="/services"
                className="animated-button text-xs sm:text-sm px-6 sm:px-8 min-w-[240px] sm:min-w-[240px] h-[52px]"
              >
                <svg viewBox="0 0 24 24" className="arr-2" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"></path>
                </svg>
                <span className="text" style={{ textShadow: 'none' }}>Click to view more details</span>
                <span className="circle"></span>
                <svg viewBox="0 0 24 24" className="arr-1" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default Services;
