import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Helmet } from "react-helmet";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <>
      <Helmet>
        <title>Page Not Found | Chalaka Dulanga Photography</title>
        <meta name="description" content="The page you are looking for does not exist. Return to the Chalaka Dulanga Photography homepage." />
      </Helmet>

      <Navbar />

      <div className="min-h-[70vh] flex items-center justify-center bg-background">
        <div className="text-center px-6">
          <h1 className="font-display text-6xl font-light mb-6">404</h1>
          <p className="text-xl text-foreground/70 mb-8">The page you are looking for cannot be found.</p>
          <a
            href="/"
            className="px-8 py-3 bg-foreground text-background hover:bg-foreground/90 transition-colors text-sm tracking-wider uppercase"
          >
            Return to Home
          </a>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default NotFound;
