const puppeteer = require('puppeteer');

(async () => {
    // Launch the Brave browser
    const browser = await puppeteer.launch({
        executablePath: '/path/to/brave', // Adjust this path to your Brave installation
        headless: false // Set to true if you don't want to see the browser
    });
    const page = await browser.newPage();

    try {
        // Navigate to the specified GitHub URL
        await page.goto('https://github.com/punkpeye/awesome-mcp-servers?tab=readme-ov-file#social-media', {
            waitUntil: 'networkidle2'
        });

        // Wait for the MCPs section to load
        await page.waitForSelector('h2:contains("MCPs that enhance coding abilities")', { timeout: 10000 });

        // Extract the list of MCPs
        const mcps = await page.evaluate(() => {
            const mcpElements = document.querySelectorAll('h2:contains("MCPs that enhance coding abilities") + ul li');
            return Array.from(mcpElements).map(el => el.innerText);
        });

        // Log the extracted MCPs
        console.log('Extracted MCPs:', mcps);
    } catch (error) {
        console.error('Error extracting MCPs:', error);
    } finally {
        // Close the browser
        await browser.close();
    }
})();