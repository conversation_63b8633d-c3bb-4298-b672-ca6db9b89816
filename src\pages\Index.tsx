
import { useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { motion, useScroll, useTransform } from 'framer-motion';
import Navbar from '../components/Navbar';
import Hero from '../components/Hero';
import Portfolio from '../components/Portfolio';
import About from '../components/About';
import Services from '../components/Services';
import Testimonials from '../components/Testimonials';
import Footer from '../components/Footer';
import ScrollToTop from '../components/ScrollToTop';
import LoadingAnimation from '../components/LoadingAnimation';
import useParallax from '../hooks/useParallax';

const Index = () => {
  const sectionsRef = useRef<HTMLDivElement>(null);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Use Framer Motion's scroll utilities
  const { scrollYProgress } = useScroll();
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1.15]);

  // Use parallax effect for decorative elements
  const { offset: parallaxOffset } = useParallax({
    speed: 0.15,
    direction: 'up'
  });

  // Handle scroll events for various effects
  useEffect(() => {
    const handleScroll = () => {
      // Calculate scroll progress (0 to 1)
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollTop = window.scrollY;
      const scrollableHeight = documentHeight - windowHeight;
      const progress = Math.min(scrollTop / scrollableHeight, 1);

      setScrollProgress(progress);
    };

    // Scroll to top on page load
    window.scrollTo(0, 0);

    // Initialize intersection observer for section animations
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-section-enter');
          entry.target.classList.remove('animate-section-hidden');
          // Add more advanced animation class based on index
          const sectionIndex = Array.from(sectionsRef.current?.children || []).indexOf(entry.target);
          entry.target.classList.add(`reveal-section-${sectionIndex % 3}`);
        }
      });
    }, observerOptions);

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Observe all sections except hero
    if (sectionsRef.current) {
      const sections = sectionsRef.current.querySelectorAll('.animate-on-scroll');
      sections.forEach(section => {
        section.classList.add('animate-section-hidden');
        sectionObserver.observe(section);
      });
    }

    return () => {
      if (sectionsRef.current) {
        const sections = sectionsRef.current.querySelectorAll('.animate-on-scroll');
        sections.forEach(section => sectionObserver.unobserve(section));
      }
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <>
      {/* Loading animation */}
      {isLoading && <LoadingAnimation onComplete={() => setIsLoading(false)} />}

      <motion.div
        className="min-h-screen bg-background relative overflow-hidden z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoading ? 0 : 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <Helmet>
          <title>Chalaka Dulanga Photography | Fine Art Wedding Photography</title>
          <meta name="description" content="Chalaka Dulanga Photography specializes in fine art wedding photography, capturing timeless moments with elegance and emotion across Sri Lanka." />
        </Helmet>

        {/* Decorative elements with Framer Motion */}
        <motion.div
          className="fixed top-0 right-0 w-96 h-96 bg-gradient-to-b from-champagne-100/10 to-transparent rounded-full blur-3xl pointer-events-none z-0"
          style={{
            y: useTransform(scrollYProgress, [0, 1], [0, parallaxOffset * 2])
          }}
        />
        <motion.div
          className="fixed bottom-0 left-0 w-80 h-80 bg-gradient-to-t from-champagne-200/10 to-transparent rounded-full blur-3xl pointer-events-none z-0"
          style={{
            y: useTransform(scrollYProgress, [0, 1], [0, -parallaxOffset * 1.5])
          }}
        />

        {/* Progress indicator */}
        <motion.div className="fixed top-0 left-0 w-full h-0.5 z-50">
          <motion.div
            className="h-full bg-gradient-to-r from-champagne-300 to-primary"
            style={{
              scaleX: scrollYProgress,
              transformOrigin: "0% 50%"
            }}
          />
        </motion.div>

        <Navbar />
        <Hero />

        <motion.div
          ref={sectionsRef}
          className="relative z-10"
        >
          <motion.div
            className="animate-on-scroll"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          >
            <About />
          </motion.div>

          <motion.div
            className="animate-on-scroll"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          >
            <Portfolio />
          </motion.div>

          <motion.div
            className="animate-on-scroll"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          >
            <Services />
          </motion.div>

          <motion.div
            className="animate-on-scroll"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          >
            <Testimonials />
          </motion.div>

        </motion.div>

        <Footer />
        <ScrollToTop />
      </motion.div>
    </>
  );
};

export default Index;
