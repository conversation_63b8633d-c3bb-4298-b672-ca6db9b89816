import { useEffect } from "react";
import { Helmet } from "react-helmet";
import { motion } from "framer-motion";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ScrollToTop from "@/components/ScrollToTop";
import { Star } from "lucide-react";

const StoriesPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // All client testimonials
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      quote: "Thank you for your wonderful photography, we absolutely love all the photos that you have taken 😍…as well as we will highly recommend you to anyone in need of a photographer.❤️❤️! Thank you so much mallii....🥰",
      image: "/testimonials/<PERSON><PERSON>.jpg?v=2",
      rating: 5
    },
    {
      name: "Tharind<PERSON>",
      quote: "Highly Recommended 😍♥️",
      image: "/testimonials/Tharindu <PERSON>.jpg?v=2",
      rating: 5
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      quote: "Thank you so much again <PERSON><PERSON><PERSON> malli ❤️",
      image: "/testimonials/Nilakshi.jpg",
      rating: 5
    },
    {
      name: "<PERSON><PERSON>",
      quote: "Highly recommend ❤",
      image: "/testimonials/Yohani.jpg",
      rating: 5
    },
    {
      name: "Iroshan T Lakmal",
      quote: "🤩 amazing 😻 lovely 🥰 wow 😮 thanks ☺️",
      image: "/testimonials/Iroshan T Lakmal.jpg",
      rating: 5
    },
    {
      name: "Julia YM",
      quote: "Thank you Chalaka Dulanga Photography",
      image: "/testimonials/Julia Ym.jpg",
      rating: 5
    },
    {
      name: "Sanduni Madubhashini",
      quote: "Thank you so much ❤️❤️",
      image: "/testimonials/Sanduni.jpg",
      rating: 5
    },
    {
      name: "Nishadi Vidarshika",
      quote: "Thank you Chalaka Dulanga malli❤️",
      image: "/testimonials/Nishadi.jpg",
      rating: 5
    },

    {
      name: "Harsha Chamara",
      quote: "We're so lucky to have such great pictures of our wedding. Thank you malli💜️💜️💜️",
      image: "/testimonials/Harsha.jpg",
      rating: 5
    },

    {
      name: "Dushan Kaushika Suriyampola",
      quote: "Great work malli keep it up 👏❤️",
      image: "/testimonials/Dushan.jpg",
      rating: 5
    },
    {
      name: "Dhananjaya B Wijewardhane",
      quote: "Thank you Chalaka malli for the amazing captures ❤️📸",
      image: "/testimonials/Dhananjaya.jpg?v=2",
      rating: 5
    },
    {
      name: "Nethumi Imesha",
      quote: "No words 🔥🥺",
      image: "/testimonials/Imesha.jpg",
      rating: 5
    },
    {
      name: "Nipuni Rajapaksha",
      quote: "Thank you so much malli",
      image: "/testimonials/Nipuni.jpg?v=2",
      rating: 5
    },
    {
      name: "Thilan Bandara Dassanayake",
      quote: "Thank you Chalaka Dulanga malli",
      image: "/testimonials/Thilan.jpg?v=2",
      rating: 5
    },
    {
      name: "Thamali Peiris",
      quote: "Wow thank you soo much Chalaka Dulanga Photography 😍😍",
      image: "/testimonials/Thamali.jpg",
      rating: 5
    },
    {
      name: "Jayamini Rathnayake",
      quote: "Thanks malli for making our sons day special❤its wow!",
      image: "/testimonials/Jayamini.jpg",
      rating: 5
    },
    {
      name: "Sanduni Wickramasinghe",
      quote: "Thankyou malli💖",
      image: "/testimonials/Wickramasinghe.jpg",
      rating: 5
    },
    {
      name: "Charry J Tangaro",
      quote: "Thank you very much for the awesome photos Chalaka Dulanga Photography Team 🫶🫶🫶 ❤️❤️❤️Godbless 😇😇😇",
      image: "/testimonials/Charry J Tangaro.jpg",
      rating: 5
    },
    {
      name: "Nisansala Mayadunna",
      quote: "Thank you mallii👍",
      image: "/testimonials/Nisansala.jpg",
      rating: 5
    },
    {
      name: "Nisal Anjana",
      quote: "Thank you so much malli❤️",
      image: "/testimonials/Nisal.jpg",
      rating: 5
    }
  ];

  return (
    <>
      <Helmet>
        <title>Client Stories | Chalaka Dulanga Photography</title>
        <meta name="description" content="Read real testimonials from clients who have worked with Chalaka Dulanga Photography for their wedding, engagement, and special event photography." />
      </Helmet>

      <Navbar />

      <main>
        {/* Hero Section */}
        <section className="relative h-[50vh] md:h-[60vh] overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1469371670807-013ccf25f16a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80"
              alt="Client Stories"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-black/20" />
          <div className="absolute inset-0 flex items-center justify-center text-center text-white p-6">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-display font-light tracking-wide">Client Stories</h1>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="heading-lg mb-4">Client Testimonials</h2>
              <p className="body-md max-w-2xl mx-auto text-foreground/70">
                Hear from the wonderful clients who have trusted me to capture their special moments.
              </p>
            </div>

            <div className="space-y-24">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.name}
                  className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
                >
                  <div className={`${index % 2 === 1 ? 'md:order-2' : ''}`}>
                    <div className="overflow-hidden rounded-lg">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="w-full h-[500px] object-cover hover:scale-105 transition-transform duration-700"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col justify-center">
                    <div className="flex mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-500 fill-yellow-500" />
                      ))}
                    </div>
                    <p className="text-xl md:text-2xl font-serif italic mb-6">"{testimonial.quote}"</p>
                    <h3 className="heading-sm mb-1">{testimonial.name}</h3>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-6 bg-secondary">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="heading-lg mb-6">Ready to Create Your Own Story?</h2>
            <p className="body-md max-w-2xl mx-auto mb-10 text-foreground/70">
              I'd be honored to capture your special moments and help you tell your unique love story.
            </p>
            <a
              href="/services#pre-shoot-packages"
              className="inline-block px-8 py-3 border border-foreground/30 hover:border-foreground/60 transition-all duration-300 text-sm tracking-wider uppercase"
            >
              Click to view more details
            href="/services#pre-shoot-packages"
            className="inline-block px-8 py-3 border border-foreground/30 hover:border-foreground/60 transition-all duration-300 text-sm tracking-wider uppercase"
:start_line:223
-------
          
            Click to view more details
          </a>
          </div>
        </section>
      </main>

      <Footer />
      <ScrollToTop />
    </>
  );
};

export default StoriesPage;
