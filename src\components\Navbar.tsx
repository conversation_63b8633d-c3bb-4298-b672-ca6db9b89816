
import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const isPortfolioPage = location.pathname === "/portfolio";
  const animationRef = useRef<number | null>(null);

  // Smooth scroll to top function
  const smoothScrollToTop = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Only prevent default and scroll if we're on the home page
    if (location.pathname === '/') {
      e.preventDefault();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Clean up animation on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <nav
      className={cn(
        "fixed top-0 left-0 right-0 z-50 px-4 sm:px-6 md:px-10",
        isScrolled
          ? "bg-white py-3 sm:py-4"
          : "bg-transparent py-4 sm:py-6"
      )}
    >
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <Link
          to="/"
          onClick={smoothScrollToTop}
          className={cn(
            "relative z-10 font-serif text-lg sm:text-xl md:text-2xl",
            isScrolled
              ? "text-black"
              : "text-white",
          )}
        >
          Chalaka Dulanga Photography
        </Link>

        <div className="hidden md:flex items-center gap-6 md:gap-8 lg:gap-10">
          <NavLink
            to="/"
            onClick={smoothScrollToTop}
          >
            Home
          </NavLink>
          <NavLink to="/about">About</NavLink>
          <NavLink to="/services">Services</NavLink>
          <NavLink to="/testimonials">Stories</NavLink>
          <NavLink to="/contact" className="btn-primary text-white hover:text-white active:text-white">Contact Me</NavLink>
          <a
            href="/booking"
            className="btn-primary bg-amber-600 hover:bg-amber-700 text-white hover:text-white active:text-white px-4 py-2 rounded-md transition-colors duration-200"
          >
            Book Now
          </a>
        </div>

        {/* Mobile menu button */}
        <button
          type="button"
          className="md:hidden flex flex-col gap-1.5 z-10"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle menu"
        >
          <span
            className={cn(
              "w-6 h-0.5 transition-all duration-300",
              isMobileMenuOpen
                ? "bg-white" // Always white when menu is open for visibility
                : isScrolled
                  ? "bg-foreground"
                  : isPortfolioPage
                    ? "bg-black"
                    : "bg-white",
              isMobileMenuOpen && "rotate-45 translate-y-2"
            )}
          />
          <span
            className={cn(
              "w-6 h-0.5 transition-all duration-300",
              isMobileMenuOpen
                ? "bg-white" // Always white when menu is open for visibility
                : isScrolled
                  ? "bg-foreground"
                  : isPortfolioPage
                    ? "bg-black"
                    : "bg-white",
              isMobileMenuOpen && "opacity-0"
            )}
          />
          <span
            className={cn(
              "w-6 h-0.5 transition-all duration-300",
              isMobileMenuOpen
                ? "bg-white" // Always white when menu is open for visibility
                : isScrolled
                  ? "bg-foreground"
                  : isPortfolioPage
                    ? "bg-black"
                    : "bg-white",
              isMobileMenuOpen && "-rotate-45 -translate-y-2"
            )}
          />
        </button>

        {/* Mobile menu */}
        <div className={cn(
          "fixed inset-0 flex flex-col justify-center items-center gap-8 transition-all duration-500 md:hidden",
          isScrolled
            ? "bg-background"
            : "bg-slate-900/95", // Darker background when not scrolled for better contrast
          isMobileMenuOpen
            ? "opacity-100 translate-x-0"
            : "opacity-0 translate-x-full pointer-events-none"
        )}>
          <NavLink
            to="/"
            onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
              setIsMobileMenuOpen(false);
              smoothScrollToTop(e);
            }}
            className="text-2xl"
          >
            Home
          </NavLink>
          <NavLink
            to="/about"
            onClick={() => setIsMobileMenuOpen(false)}
            className="text-2xl"
          >
            About
          </NavLink>
          <NavLink
            to="/services"
            onClick={() => setIsMobileMenuOpen(false)}
            className="text-2xl"
          >
            Services
          </NavLink>
          <NavLink
            to="/testimonials"
            onClick={() => setIsMobileMenuOpen(false)}
            className="text-2xl"
          >
            Stories
          </NavLink>
          <NavLink
            to="/contact"
            onClick={() => setIsMobileMenuOpen(false)}
            className="btn-primary text-xl mt-4 text-white hover:text-white active:text-white"
          >
            Contact Me
          </NavLink>
          <a
            href="/booking"
            onClick={() => setIsMobileMenuOpen(false)}
            className="btn-primary bg-amber-600 hover:bg-amber-700 text-xl mt-2 text-white hover:text-white active:text-white px-6 py-3 rounded-md transition-colors duration-200"
          >
            Book Now
          </a>
        </div>
      </div>
    </nav>
  );
};

const NavLink = ({
  to,
  children,
  className,
  ...props
}: {
  to: string;
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const isPortfolioPage = location.pathname === "/portfolio";
  const isMobileLink = className?.includes("text-2xl") || false;

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  return (
    <Link
      to={to}
      className={cn(
        "font-serif",
        isScrolled
          ? "text-black hover:text-black"
          : isMobileLink
            ? "text-white hover:text-white/80" // Mobile menu links are always white when not scrolled
            : isPortfolioPage
              ? "text-black hover:text-black/80"
              : "text-white hover:text-white/80",
        className === "btn-primary"
          ? isScrolled
            ? "px-4 py-2 text-black hover:text-white"
            : isPortfolioPage
              ? "px-4 py-2 border border-white/40 hover:border-white text-white hover:text-white"
              : "px-4 py-2 border border-white/40 hover:border-white text-white hover:text-white"
          : "link-underline",
        className,
      )}
      {...props}
    >
      {children}
    </Link>
  );
};

export default Navbar;
