
import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

// Selected testimonials for the home page
const testimonials = [
  {
    id: 1,
    quote: "Thank you for your wonderful photography, we absolutely love all the photos that you have taken 😍…as well as we will highly recommend you to anyone in need of a photographer.❤️❤️!",
    name: "<PERSON><PERSON>",
    image: "/testimonials/<PERSON><PERSON>.jpg?v=2"
  },
  {
    id: 2,
    quote: "We're so lucky to have such great pictures of our wedding. Thank you malli💜️💜️💜️",
    name: "<PERSON><PERSON><PERSON>",
    image: "/testimonials/Harsha.jpg"
  },
  {
    id: 3,
    quote: "Thank you <PERSON><PERSON><PERSON> mall<PERSON> for the amazing captures ❤️📸",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    image: "/testimonials/Dhananjaya.jpg?v=2"
  },
  {
    id: 4,
    quote: "Highly Recommended 😍♥️",
    name: "<PERSON>harind<PERSON>sen<PERSON>",
    image: "/testimonials/Tharindu Dharmasena.jpg?v=2"
  }
];

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const intervalRef = useRef<number | null>(null);

  useEffect(() => {
    // Auto-rotate testimonials
    intervalRef.current = window.setInterval(() => {
      setActiveIndex(prev => (prev + 1) % testimonials.length);
    }, 6000);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, []);

  const handleDotClick = (index: number) => {
    setActiveIndex(index);
    // Reset interval timer when manually changing
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = window.setInterval(() => {
      setActiveIndex(prev => (prev + 1) % testimonials.length);
    }, 6000);
  };

  return (
    <section id="testimonials" className="section-padding relative overflow-hidden bg-foreground text-background">
      <div className="content-container">
        <div className="text-center mb-10 md:mb-16">
          <h2 className="heading-lg mb-4 text-background">Client Stories</h2>
          <p className="body-md max-w-xl mx-auto text-background/70">
            The emotions and experiences of couples who have trusted me to document their love stories
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 lg:gap-20 items-center">
          {/* Testimonial image */}
          <div className="lg:w-1/2 relative w-full"> {/* Added w-full for mobile stack */}
            <div className="aspect-[4/5] overflow-hidden">
              {testimonials.map((testimonial, index) => (
                <div
                  key={testimonial.id}
                  className={cn(
                    "absolute inset-0 transition-all duration-1000",
                    activeIndex === index
                      ? "opacity-100 translate-x-0"
                      : "opacity-0 translate-x-8"
                  )}
                >
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Testimonial content */}
          <div className="lg:w-1/2 flex flex-col justify-center">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className={cn(
                  "transition-all duration-1000",
                  activeIndex === index
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-8 absolute pointer-events-none"
                )}
              >
                <svg className="h-12 w-12 text-background/30 mb-6" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 32 32">
                  <path d="M10 8c-3.3 0-6 2.7-6 6v10h10V14H8c0-1.1.9-2 2-2V8zm18 0c-3.3 0-6 2.7-6 6v10h10V14h-6c0-1.1.9-2 2-2V8z"/>
                </svg>

                <p className="font-display text-xl sm:text-2xl lg:text-3xl font-light mb-6 sm:mb-8 leading-relaxed">
                  {testimonial.quote}
                </p>

                <div>
                  <h4 className="font-serif text-lg sm:text-xl">{testimonial.name}</h4>
                </div>
              </div>
            ))}

            {/* Navigation dots */}
            <div className="flex gap-3 mt-8 sm:mt-12 justify-center lg:justify-start">
              {testimonials.map((_, index) => (
                <button
                  type="button"
                  key={index}
                  onClick={() => handleDotClick(index)}
                  className={cn(
                    "w-2.5 h-2.5 rounded-full transition-all duration-300",
                    activeIndex === index
                      ? "bg-background"
                      : "bg-background/30 hover:bg-background/50"
                  )}
                  aria-label={`View testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
