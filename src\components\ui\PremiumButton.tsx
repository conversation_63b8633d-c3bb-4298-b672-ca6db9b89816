import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { usePremiumScrollEffects } from '@/hooks/usePremiumScrollEffects';

interface PremiumButtonProps {
  children: React.ReactNode;
  href?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  target?: string;
  rel?: string;
  download?: string;
  magnetic?: boolean;
}

const PremiumButton: React.FC<PremiumButtonProps> = ({
  children,
  href,
  onClick,
  variant = 'primary',
  size = 'md',
  className,
  disabled = false,
  target,
  rel,
  download,
  magnetic = true
}) => {
  const {
    magneticPosition,
    handleMagneticMove,
    handleMagneticLeave
  } = usePremiumScrollEffects();

  const baseClasses = cn(
    "relative inline-flex items-center justify-center font-medium transition-all duration-300 ease-out",
    "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black/20",
    "overflow-hidden group",
    disabled && "opacity-50 cursor-not-allowed pointer-events-none"
  );

  const variantClasses = {
    primary: "bg-black text-white hover:bg-gray-800 focus:ring-black/20",
    secondary: "bg-white text-black border border-black/20 hover:bg-gray-50 focus:ring-black/20",
    outline: "border border-black/30 text-black hover:border-black/60 hover:bg-black/5 focus:ring-black/20",
    ghost: "text-black hover:bg-black/5 focus:ring-black/20"
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm rounded-md",
    md: "px-6 py-3 text-base rounded-md",
    lg: "px-8 py-4 text-lg rounded-lg"
  };

  const combinedClasses = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  );

  const buttonContent = (
    <>
      {/* Background animation */}
      <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out" />
      
      {/* Content */}
      <span className="relative z-10 flex items-center gap-2">
        {children}
      </span>

      {/* Hover effect overlay */}
      <span className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </>
  );

  const magneticStyle = magnetic ? {
    transform: `translate(${magneticPosition.x}px, ${magneticPosition.y}px)`,
    transition: 'transform 0.3s cubic-bezier(0.22, 1, 0.36, 1)'
  } : {};

  const motionProps = {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: { duration: 0.2, ease: [0.22, 1, 0.36, 1] }
  };

  if (href) {
    return (
      <motion.a
        href={href}
        target={target}
        rel={rel}
        download={download}
        className={combinedClasses}
        style={magneticStyle}
        onMouseMove={magnetic ? handleMagneticMove : undefined}
        onMouseLeave={magnetic ? handleMagneticLeave : undefined}
        {...motionProps}
      >
        {buttonContent}
      </motion.a>
    );
  }

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={combinedClasses}
      style={magneticStyle}
      onMouseMove={magnetic ? handleMagneticMove : undefined}
      onMouseLeave={magnetic ? handleMagneticLeave : undefined}
      {...motionProps}
    >
      {buttonContent}
    </motion.button>
  );
};

export default PremiumButton;
