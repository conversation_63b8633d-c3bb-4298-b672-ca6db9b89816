
import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useIsMobile } from '../hooks/use-mobile'; // Added import
import { Button } from './ui/button'; // Import the Button component

const Hero = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile(); // Added hook call

  const desktopImages = [
    "/Asiri & Shrimali/487316291_973336151664462_4540413166843432055_n.jpg",
    "/Chalani & Charatha/495428849_1001798828818194_3017667867947218284_n.jpg",
    "/Sahasra & Tharindu/493609585_992727609725316_622908764844576113_n.jpg",
    "/<PERSON><PERSON> & D<PERSON>a/484790846_964656452532432_53661832893256839_n.jpg",
  ];

  const mobileImages = [
    "/Mobile-1.jpg",
    "/Mobile-2.jpg",
    "/Mobile-3.jpg",
    "/Mobile-4.jpg",
  ];

  const images = isMobile ? mobileImages : desktopImages; // Conditional image selection
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Text animation variants
  const lineVariants = { // Renamed from titleVariants for clarity
    hidden: { opacity: 0 }, // Simpler hidden state, character animation will handle y
    visible: (i: number) => ({
      opacity: 1,
      transition: {
        delay: i * 0.2, // Delay for each line/block before its characters start animating
        duration: 0.1, // Short duration for the container, character animation is main
      }
    })
  };

  // Staggered animation for text blocks
  const textBlockContainerVariants = { // Renamed from containerVariants
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3, // Stagger between each AnimatedTextCharacter block
        delayChildren: 0.5  // Initial delay before any text animation starts
      }
    }
  };

// New component for character-by-character animation
const AnimatedTextCharacter = ({ text, el: WrapperComponent = motion.div, className, customLineIndex }: { text: string, el?: any, className?: string, customLineIndex: number }) => {
  // Split text into words and sequences of spaces, preserving them. Filter out empty strings.
  const parts = text.split(/(\s+)/).filter(part => part.length > 0);

  const letterVariants = {
    hidden: { opacity: 0, y: 15, rotateX: -90 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      rotateX: 0,
      transition: {
        delay: i * 0.035, // Stagger delay for each letter
        duration: 0.4,
        ease: [0.22, 1, 0.36, 1]
      }
    })
  };
  
  // Variants for the container of letters.
  // The delayChildren ensures the line animation starts after its designated line delay.
  // Staggering of individual letters is handled by the `custom` prop on letterVariants.
  const lineContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: lineVariants.visible(customLineIndex).transition.delay,
        // staggerChildren is removed here as letterVariants' `custom` prop handles individual letter delays
      }
    }
  };

  let globalLetterIndex = 0; // To ensure continuous stagger across words and spaces

  return (
    <WrapperComponent variants={lineContainerVariants} initial="hidden" animate="visible" className={className}>
      <span className="sr-only">{text}</span> {/* For SEO and accessibility */}
      {parts.map((part, partIndex) => {
        if (/\s+/.test(part)) { // If the part is one or more whitespace characters
          return Array.from(part).map((spaceChar, spaceCharIndex) => (
            <motion.span
              key={`space-${partIndex}-${spaceCharIndex}-${customLineIndex}`}
              variants={letterVariants}
              custom={globalLetterIndex++}
              style={{ display: 'inline-block', willChange: 'transform, opacity' }}
            >
              {spaceChar === " " ? "\u00A0" : spaceChar} {/* Render non-breaking space for actual spaces */}
            </motion.span>
          ));
        } else { // If the part is a word
          return (
            <span
              key={`word-${partIndex}-${customLineIndex}`}
              style={{ display: 'inline-block', whiteSpace: 'nowrap' }} // Prevents word from breaking internally
            >
              {Array.from(part).map((letter, letterInWordIndex) => (
                <motion.span
                  key={`${letter}-${partIndex}-${letterInWordIndex}-${customLineIndex}`}
                  variants={letterVariants}
                  custom={globalLetterIndex++}
                  style={{ display: 'inline-block', willChange: 'transform, opacity' }}
                >
                  {letter}
                </motion.span>
              ))}
            </span>
          );
        }
      })}
    </WrapperComponent>
  );
};

  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === images.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // Change image every 5 seconds
    return () => clearTimeout(timer);
  }, [currentImageIndex, images]); // Updated dependency array

  return (
    <motion.div
      ref={heroRef}
      className="relative flex flex-col h-screen overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
    >
      {/* Hero background image with overlay */}
      <div className="absolute inset-0 bg-black">
        <motion.div
          className="absolute inset-0 opacity-60 bg-gradient-to-b from-black/50 via-transparent to-black/90 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 1.5, delay: 0.5 }}
        />
        <AnimatePresence mode="wait">
          <motion.img
            key={currentImageIndex}
            src={images[currentImageIndex]}
            alt="Wedding slideshow"
            className="absolute inset-0 w-full h-full object-cover"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1.5, ease: "easeInOut" }} // Smooth fade duration
          />
        </AnimatePresence>
      </div>

      {/* Hero content with enhanced animations */}
      <motion.div
        ref={textRef}
        className="relative z-20 flex flex-col items-center justify-center h-full text-center text-white px-4 sm:px-6 md:px-8"
      >
        {/* Floating elements */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <motion.div
            className="absolute top-[15%] left-[10%] w-20 h-20 border border-white/10 rounded-full opacity-40"
            animate={{
              y: [0, -20, 0],
              x: [0, 10, 0]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-[25%] right-[15%] w-32 h-32 border border-white/10 rounded-full opacity-30"
            animate={{
              y: [0, 30, 0],
              x: [0, -15, 0]
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute top-[40%] right-[20%] w-16 h-16 border border-white/10 rounded-full opacity-20"
            animate={{
              y: [0, 15, 0],
              x: [0, -10, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />
        </div>

        <motion.div
          variants={textBlockContainerVariants}
          initial="hidden"
          animate="visible"
          className="flex flex-col items-center"
        >
          <AnimatedTextCharacter
            text="Fine Art Photography"
            el={motion.div}
            className="tracking-widest text-xs sm:text-sm uppercase mb-1 sm:mb-2 text-white/70"
            customLineIndex={0}
          />
          
          <AnimatedTextCharacter
            text="Chalaka Dulanga Photography"
            el={motion.h1}
            className="font-serif italic font-light tracking-wide text-sm sm:text-base md:text-xl lg:text-2xl mb-2 sm:mb-4 opacity-90"
            customLineIndex={1}
          />

          {/* For the H2 with mixed styling, we might need to animate parts separately or accept simpler animation */}
          {/* Option 1: Animate whole H2 as one block */}
           <AnimatedTextCharacter
            text="Capturing Timeless Moments on Your Special Day"
            el={motion.h2}
            className="heading-xl text-white max-w-3xl mb-4 sm:mb-6" // Note: text-gradient might not work well with span-per-char
            customLineIndex={2}
          />
          {/* If text-gradient is crucial and character animation conflicts, consider this:
           <motion.h2
             variants={lineVariants} // Use lineVariants for the block
             custom={2}
             className="heading-xl text-white max-w-3xl mb-6"
           >
             <span className="text-gradient">Capturing Timeless Moments</span> on Your Special Day
           </motion.h2>
          */}

          <AnimatedTextCharacter
            text="Luxury wedding photography that tells your unique love story with elegance and emotion"
            el={motion.p}
            className="body-lg max-w-xl mx-auto mb-6 sm:mb-8 opacity-90"
            customLineIndex={3}
          />

          {/* Add the "View Packages" button */}
          <Link to="/services">
            <motion.div
              whileTap={{ scale: 0.95 }}
              className="inline-block"
            >
              <Button size="lg" className="mt-6 sm:mt-8 bg-white/10 backdrop-blur-sm border border-white/20 text-white transition-all duration-300 hover:bg-white/20 hover:border-white/30 hover:text-white">
                View Packages
              </Button>
            </motion.div>
          </Link>

          {/* Add the "Book Now" button */}
          <a
            href="https://booking.chalakadulangaphotography.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            <motion.div
              whileTap={{ scale: 0.95 }}
              className="inline-block"
            >
              <Button size="lg" className="mt-4 bg-white/10 backdrop-blur-sm border border-white/20 text-white transition-all duration-300 hover:bg-white/20 hover:border-white/30 hover:text-white">
                Book Now
              </Button>
            </motion.div>
          </a>

        </motion.div>
      </motion.div>

    </motion.div>
  );
};

export default Hero;
