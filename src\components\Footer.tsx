
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-secondary py-16 border-t border-champagne-100">
      <div className="content-container">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
          {/* Logo and info */}
          <div>
            <Link
              to="/"
              className="inline-block font-serif text-lg mb-2 hover:text-foreground/80 transition-colors duration-300"
            >
              Chalaka Dulanga Photography
            </Link>
            <p className="text-foreground/70 text-sm max-w-xs leading-relaxed">
              Fine art wedding photography capturing timeless moments for discerning couples worldwide.
            </p>

            <div className="mt-6">
              <p className="text-foreground/70 text-sm">
                Based in Sri Lanka. Available Islandwide.
              </p>
            </div>
          </div>

          {/* Navigation */}
          <div className="md:text-center">
            <h3 className="font-serif text-lg mb-4">Explore</h3>
            <nav className="flex flex-col gap-2">
              <Link to="/" className="text-foreground/70 hover:text-foreground transition-all duration-300 hover:translate-x-1">Home</Link>
              <Link to="/about" className="text-foreground/70 hover:text-foreground transition-all duration-300 hover:translate-x-1">About</Link>
              <Link to="/services" className="text-foreground/70 hover:text-foreground transition-all duration-300 hover:translate-x-1">Packages</Link>
              <Link to="/testimonials" className="text-foreground/70 hover:text-foreground transition-all duration-300 hover:translate-x-1">Stories</Link>
              <Link to="/contact" className="text-foreground/70 hover:text-foreground transition-all duration-300 hover:translate-x-1">Contact</Link>
            </nav>
          </div>

          {/* Connect */}
          <div className="md:text-right">
            <h3 className="font-serif text-lg mb-4">Connect</h3>
            <div className="flex flex-col gap-2">
              <a
                href="mailto:<EMAIL>"
                className="text-foreground/70 hover:text-foreground transition-colors"
              >
                <EMAIL>
              </a>
              <a
                href="tel:+94763249526"
                className="text-foreground/70 hover:text-foreground transition-colors"
              >
                +94 76 324 9526
              </a>
              <a
                href="https://www.instagram.com/chalaka_dulanga_photography_/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground/70 hover:text-foreground transition-colors"
              >
                Instagram
              </a>
              <a
                href="https://www.facebook.com/chalakadulangaphotography/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground/70 hover:text-foreground transition-colors"
              >
                Facebook
              </a>
              <a
                href="https://www.tiktok.com/@chalakadulanga98"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground/70 hover:text-foreground transition-colors"
              >
                TikTok
              </a>
              <a
                href="https://wa.me/94763249526"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground/70 hover:text-foreground transition-colors"
              >
                WhatsApp
              </a>
            </div>
          </div>
        </div>

        {/* Bottom credits */}
        <div className="border-t border-champagne-100 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center text-sm text-foreground/50">
          <p>&copy; {currentYear} Chalaka Dulanga Photography. All rights reserved.</p>
          <div className="flex gap-4 mt-4 md:mt-0">
            <Link to="/privacy-policy" className="hover:text-foreground/70 transition-all duration-300">Privacy Policy</Link>
            <Link to="/terms" className="hover:text-foreground/70 transition-all duration-300">Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
