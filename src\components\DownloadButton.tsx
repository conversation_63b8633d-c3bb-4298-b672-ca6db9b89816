import { motion } from 'framer-motion';
import { Download } from 'lucide-react';

interface DownloadButtonProps {
  text: string;
  onClick?: () => void;
  href?: string;
  className?: string;
}

const DownloadButton = ({ text, onClick, href, className = '' }: DownloadButtonProps) => {
  return (
    <motion.div
      className={`relative ${className}`}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.98 }}
    >
      <a
        href={href}
        onClick={onClick}
        className="relative overflow-hidden h-12 px-8 flex items-center justify-center rounded-full bg-foreground text-background cursor-pointer group"
        download={href ? true : undefined}
      >
        <span className="relative z-10 flex items-center gap-2 font-medium tracking-wide">
          {text} <Download className="h-4 w-4" />
        </span>
        <motion.span
          className="absolute inset-0 bg-gradient-to-r from-champagne-300 to-primary"
          initial={{ scaleX: 0 }}
          whileHover={{ scaleX: 1 }}
          transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
          style={{ transformOrigin: 'left' }}
        />
      </a>
    </motion.div>
  );
};

export default DownloadButton;
