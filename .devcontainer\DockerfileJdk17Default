FROM openjdk:17-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive

ENV JAVA_HOME=/usr/local/openjdk-17
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# Verify Java installation and show output explicitly
RUN set -x && \
    java -version 2>&1 && \
    echo "JAVA_HOME: $JAVA_HOME" && \
    which java


# Install system dependencies and Python 3.11
RUN apt-get update && \
    apt-get install --no-install-recommends -y \
    curl \
    build-essential \
    software-properties-common \
    openssh-server \
    tmux \
    sudo \
    wget \
    git \
    gnupg2 \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncursesw5-dev \
    libffi-dev \
    vim \
    jq \
    supervisor \
    liblzma-dev \
    libgdbm-dev \
    util-linux

# Download and compile Python 3.11
ENV PYTHON_VERSION=3.11.6
RUN wget https://www.python.org/ftp/python/${PYTHON_VERSION}/Python-${PYTHON_VERSION}.tar.xz && \
    tar -xf Python-${PYTHON_VERSION}.tar.xz && \
    cd Python-${PYTHON_VERSION} && \
    ./configure --enable-optimizations --with-ensurepip=install && \
    make -j$(nproc) && \
    make altinstall


RUN curl -fsSL https://code-server.dev/install.sh | sh && \
    # Install any VS Code extensions you want
    code-server --install-extension ms-python.python || echo "Failed to install vscode extension"

# Clean up build dependencies and source files
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /Python-${PYTHON_VERSION}*

# Symlink python3.11 to python
RUN ln -sf /usr/local/bin/python3.11 /usr/bin/python && \
    ln -sf /usr/local/bin/pip3.11 /usr/bin/pip

# Verify the Python version
RUN python --version

WORKDIR /app

RUN python -m pip install typer

COPY .devcontainer/entrypoint.sh .devcontainer/entrypoint.sh
RUN mkdir -p /root/.config/code-server
COPY .devcontainer/code_server_config.yaml /root/.config/code-server/config.yaml
COPY .devcontainer/context_params.json /root/.config/context_params.json

# Set permissions for entrypoint script
RUN chmod +x /app/.devcontainer/entrypoint.sh

RUN curl -fsSL https://code-server.dev/install.sh | sh && \
    # Install any VS Code extensions you want
    code-server --install-extension ms-python.python || echo "Failed to install vscode extension"

# Expose ports
EXPOSE 3000 8001 22 1111 27017 8000 8088
EXPOSE 55000-55999

ENTRYPOINT ["bash", "/app/.devcontainer/entrypoint.sh"]