
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { width: 100%; max-width: 600px; margin: 0 auto; }
            .header { background-color: #4a6da7; color: white; padding: 15px; text-align: center; }
            .content { padding: 20px; }
            .footer { background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 12px; }
            .details { margin: 20px 0; }
            .details table { width: 100%; border-collapse: collapse; }
            .details table td, .details table th { border: 1px solid #ddd; padding: 8px; }
            .details table th { background-color: #f2f2f2; text-align: left; }
            .button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>New Booking Request - {{invoice_number}} - {{event_type}}</h1>
            </div>
            <div class="content">
                <p>A new booking request has been received:</p>
                
                <div class="details">
                    <table>
                        <tr>
                            <th>Booking ID</th>
                            <td>{{booking_id}}</td>
                        </tr>
                        <tr>
                            <th>Invoice Number</th>
                            <td>{{invoice_number}}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>{{status}}</td>
                        </tr>
                        <tr>
                            <th>Created Date</th>
                            <td>{{created_at}}</td>
                        </tr>
                        <tr>
                            <th>Client Name</th>
                            <td>{{contact_info.phone}}</td>
                        </tr>
                        <tr>
                            <th>Client Email</th>
                            <td>{{contact_info.email}}</td>
                        </tr>
                        <tr>
                            <th>Client Phone</th>
                            <td>{{contact_info.phone}}</td>
                        </tr>
                        <tr>
                            <th>Event Type</th>
                            <td>{{event_type}}</td>
                        </tr>
                        <tr>
                            <th>Bride's Name</th>
                            <td>{{couple_info.bride_name}}</td>
                        </tr>
                        <tr>
                            <th>Groom's Name</th>
                            <td>{{couple_info.groom_name}}</td>
                        </tr>
                        {% if event_type == "Engagement Session" %}
                        <tr>
                            <th>Event Date</th>
                            <td>{{engagement_details.date}}</td>
                        </tr>
                        <tr>
                            <th>Venue</th>
                            <td>{{engagement_details.venue_name}}</td>
                        </tr>
                        <tr>
                            <th>Location</th>
                            <td>{{engagement_details.venue_location}}</td>
                        </tr>
                        <tr>
                            <th>Package</th>
                            <td>{{engagement_details.package}}</td>
                        </tr>
                        <tr>
                            <th>Registration Time</th>
                            <td>{{engagement_details.registration_time}}</td>
                        </tr>
                        <tr>
                            <th>Ring Exchange Time</th>
                            <td>{{engagement_details.ring_exchange_time}}</td>
                        </tr>
                        <tr>
                            <th>Event End Time</th>
                            <td>{{engagement_details.event_end_time}}</td>
                        </tr>
                        <tr>
                            <th>Guest Count</th>
                            <td>{{engagement_details.guest_count}}</td>
                        </tr>
                        {% if engagement_details.makeup_artist %}
                        <tr>
                            <th>Makeup Artist</th>
                            <td>{{engagement_details.makeup_artist}}</td>
                        </tr>
                        {% endif %}
                        {% endif %}
                        
                        {% if wedding_details %}
                        <tr>
                            <th>Wedding Date</th>
                            <td>{{wedding_details.date}}</td>
                        </tr>
                        <tr>
                            <th>Wedding Venue</th>
                            <td>{{wedding_details.venue_name}}</td>
                        </tr>
                        <tr>
                            <th>Wedding Package</th>
                            <td>{{wedding_details.package}}</td>
                        </tr>
                        <tr>
                            <th>Photo Location</th>
                            <td>{{wedding_details.photo_location}}</td>
                        </tr>
                        <tr>
                            <th>Ceremony Start Time</th>
                            <td>{{wedding_details.ceremony_start_time}}</td>
                        </tr>
                        <tr>
                            <th>Photography Start Time</th>
                            <td>{{wedding_details.photography_start_time}}</td>
                        </tr>
                        <tr>
                            <th>Event End Time</th>
                            <td>{{wedding_details.event_end_time}}</td>
                        </tr>
                        <tr>
                            <th>Guest Count</th>
                            <td>{{wedding_details.guest_count}}</td>
                        </tr>
                        {% if wedding_details.makeup_artist %}
                        <tr>
                            <th>Makeup Artist</th>
                            <td>{{wedding_details.makeup_artist}}</td>
                        </tr>
                        {% endif %}
                        {% endif %}
                        
                        {% if homecoming_details %}
                        <tr>
                            <th>Homecoming Date</th>
                            <td>{{homecoming_details.date}}</td>
                        </tr>
                        <tr>
                            <th>Homecoming Venue</th>
                            <td>{{homecoming_details.venue_name}}</td>
                        </tr>
                        <tr>
                            <th>Homecoming Package</th>
                            <td>{{homecoming_details.package}}</td>
                        </tr>
                        <tr>
                            <th>Photo Location</th>
                            <td>{{homecoming_details.photo_location}}</td>
                        </tr>
                        <tr>
                            <th>Photography Start Time</th>
                            <td>{{homecoming_details.photography_start_time}}</td>
                        </tr>
                        <tr>
                            <th>Event End Time</th>
                            <td>{{homecoming_details.event_end_time}}</td>
                        </tr>
                        <tr>
                            <th>Guest Count</th>
                            <td>{{homecoming_details.guest_count}}</td>
                        </tr>
                        {% if homecoming_details.makeup_artist %}
                        <tr>
                            <th>Makeup Artist</th>
                            <td>{{homecoming_details.makeup_artist}}</td>
                        </tr>
                        {% endif %}
                        {% endif %}
                        
                        {% if preshoot_details %}
                        <tr>
                            <th>Pre-Shoot Date</th>
                            <td>{{preshoot_details.date}}</td>
                        </tr>
                        <tr>
                            <th>Pre-Shoot Location</th>
                            <td>{{preshoot_details.location}}</td>
                        </tr>
                        <tr>
                            <th>Outfit Changes</th>
                            <td>{{preshoot_details.outfit_changes}}</td>
                        </tr>
                        {% endif %}
                        
                        {% if contact_info.additional_notes %}
                        <tr>
                            <th>Additional Notes</th>
                            <td>{{contact_info.additional_notes}}</td>
                        </tr>
                        {% endif %}
                        
                        {% if contact_info.how_heard_about %}
                        <tr>
                            <th>How They Heard About You</th>
                            <td>{{contact_info.how_heard_about}}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
                
                <p>Click the button below to confirm this booking:</p>
                <p><a href="{{confirm_url}}" class="button">Confirm Booking</a></p>
                
                <p>Add this event to your calendar:</p>
                <p><a href="{{calendar_url}}" class="button">Add to Calendar</a></p>
                
                <p>Invoice Management:</p>
                <p>
                    <a href="{{add_costs_url}}" class="button">Add Costs</a>
                    <a href="{{preview_invoice_url}}" class="button">Preview Invoice</a>
                    <a href="{{generate_invoice_url}}" class="button">Generate Invoice</a>
                </p>
            </div>
            <div class="footer">
                <p>&copy; Chalaka Dulanga Photography. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    