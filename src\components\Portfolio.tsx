
import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import useParallax from '@/hooks/useParallax';

// Categories removed as requested

const portfolioItems = [
  {
    id: 1,
    category: 'weddings',
    image: '/Mobile-1.jpg',
    title: 'Asiri & Shrimali',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 2,
    category: 'weddings',
    image: '/<PERSON> & <PERSON>ith<PERSON>/487313407_973486634982747_921204117200231749_n.jpg',
    title: '<PERSON> & <PERSON><PERSON><PERSON>',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 3,
    category: 'weddings',
    image: '/Nilak<PERSON> & Charith/485150020_965624465768964_3615356717551209126_n.jpg',
    title: 'Nilak<PERSON> & Charith',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 4,
    category: 'pre-shoot',
    image: '/<PERSON><PERSON><PERSON> &  Sampath.jpg',
    title: '<PERSON><PERSON><PERSON> & Sampath',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 5,
    category: 'weddings',
    image: '/<PERSON><PERSON> & D<PERSON>a/484790846_964656452532432_53661832893256839_n.jpg',
    title: 'Shehani & Dharshana',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 6,
    category: 'weddings',
    image: '/Ayesha & Nadeera/481253141_956446116686799_4638859125968788165_n.jpg',
    title: 'Ayesha & Nadeera',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 7,
    category: 'weddings',
    image: '/Chalani & Charatha/494680959_1001799142151496_3499455149897063163_n.jpg',
    title: 'Chalani & Charatha',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 8,
    category: 'weddings',
    image: '/Malki & Ishara/495569005_1003799068618170_1827890246146434189_n.jpg',
    title: 'Malki & Ishara',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 9,
    category: 'weddings',
    image: '/Pasindu & Nipuni/481089621_953059527025458_2834227947591567167_n.jpg',
    title: 'Pasindu & Nipuni',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 10,
    category: 'weddings',
    image: '/Piumi & Madhawa/484736549_964071875924223_5257531857905415_n.jpg',
    title: 'Piumi & Madhawa',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 11,
    category: 'weddings',
    image: '/Sahasra & Tharindu/492370774_992728956391848_1268320874717235336_n.jpg',
    title: 'Sahasra & Tharindu',
    location: '',
    orientation: 'landscape'
  },
  {
    id: 12,
    category: 'weddings',
    image: '/Shanika and Shanaka/481077734_955329966798414_6514107462923680006_n.jpg',
    title: 'Shanika and Shanaka',
    location: '',
    orientation: 'landscape'
  }
];

const Portfolio = () => {
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [inViewItems, setInViewItems] = useState<number[]>([]);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Use parallax effect for subtle movement
  const { offset: parallaxOffset } = useParallax({
    speed: 0.05,
    direction: 'up'
  });



  // Show all portfolio items without filtering
  const filteredItems = portfolioItems;

  // Track mouse movement for magnetic effect
  const handleMouseMove = (e: React.MouseEvent) => {
    setMousePosition({
      x: e.clientX,
      y: e.clientY
    });
  };

  // Check if items are in view
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const id = Number(entry.target.getAttribute('data-id'));

        if (entry.isIntersecting) {
          setInViewItems(prev => [...prev, id]);
        } else {
          setInViewItems(prev => prev.filter(itemId => itemId !== id));
        }
      });
    }, observerOptions);

    itemRefs.current.forEach(ref => {
      if (ref) observer.observe(ref);
    });

    return () => {
      itemRefs.current.forEach(ref => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, [filteredItems]);

  return (
    <section
      id="portfolio"
      className="section-padding bg-secondary"
      onMouseMove={handleMouseMove}
    >
      <div className="content-container">
        <div className="text-center mb-10 md:mb-16">
          <h2 className="heading-lg mb-4">Selected Works</h2>
          <p className="body-md max-w-xl mx-auto text-foreground/70">
            Curated collections of my most cherished moments, captured with passion and artistic vision.
          </p>
        </div>

        {/* Category filter removed as requested */}

        {/* Portfolio grid - Optimized for mobile visibility */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 md:gap-10">
          {filteredItems.map((item, index) => (
            <div
              key={item.id}
              ref={el => itemRefs.current[index] = el}
              data-id={item.id}
              className={cn(
                "group relative cursor-pointer overflow-hidden transition-all duration-700",
                "min-h-0", // Prevent layout issues on mobile
                item.orientation === 'portrait' ? 'row-span-2' : '',
                inViewItems.includes(item.id) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12',
              )}
              style={{
                transitionDelay: `${index * 100}ms`,
              }}
              onMouseEnter={() => setHoveredItem(item.id)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <div
                className={cn(
                  "aspect-[3/4] overflow-hidden",
                  hoveredItem === item.id && "z-10"
                )}
                style={{
                  transform: item.id === hoveredItem
                    ? `scale(1.02) translateY(${parallaxOffset * 0.2}px)`
                    : `scale(1) translateY(${parallaxOffset * 0.1}px)`,
                  transition: 'transform 0.5s cubic-bezier(0.22, 1, 0.36, 1)'
                }}
              >
                <img
                  src={item.image}
                  alt={item.title}
                  className={cn(
                    "w-full h-full object-cover transition-all duration-1000 ease-out",
                    hoveredItem === item.id
                      ? "scale-105 filter brightness-90"
                      : "scale-100 filter brightness-100"
                  )}
                  style={{
                    transformOrigin: hoveredItem === item.id
                      ? `${(mousePosition.x / window.innerWidth) * 100}% ${(mousePosition.y / window.innerHeight) * 100}%`
                      : 'center center'
                  }}
                />

                {/* Clean overlay without gradient */}
                <div
                  className={cn(
                    "absolute inset-0 bg-black/20 transition-opacity duration-500",
                    hoveredItem === item.id ? "opacity-100" : "opacity-0"
                  )}
                />
              </div>

              {/* Clean caption without gradient - Always visible on mobile */}
              <div
                className={cn(
                  "absolute inset-x-0 bottom-0 p-3 sm:p-4 md:p-6 flex flex-col transition-all duration-300 ease-out",
                  "bg-black/75 backdrop-blur-sm", // Clean background without gradient
                  // Always visible on mobile, hover effect on desktop
                  "opacity-100 translate-y-0", // Always visible on all devices
                  "md:opacity-0 md:translate-y-2", // Hidden by default on desktop
                  hoveredItem === item.id && "md:opacity-100 md:translate-y-0" // Show on hover for desktop
                )}
              >
                <h3 className="font-display text-sm sm:text-base md:text-lg text-white mb-1 relative leading-tight font-medium">
                  {item.title}
                  <span className="absolute left-0 bottom-0 w-0 h-px bg-white/50 group-hover:w-full transition-all duration-300 ease-out"></span>
                </h3>
                {item.location && (
                  <p className="font-light text-white/90 text-xs sm:text-sm">{item.location}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-10 md:mt-14">
          <a
            href="https://www.facebook.com/chalakadulangaphotography/photos_albums"
            className="px-6 py-2 text-xs sm:px-8 sm:py-3 sm:text-sm border border-foreground/30 hover:border-foreground/60 transition-all duration-300 tracking-wider uppercase relative overflow-hidden group"
            target="_blank" // Add target="_blank" to open in a new tab
            rel="noopener noreferrer" // Add rel for security best practice
          >
            <span className="relative z-10">View Full Portfolio</span>
            <span className="absolute left-0 bottom-0 h-0 w-full bg-foreground/5 transition-all duration-300 group-hover:h-full"></span>
          </a>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
