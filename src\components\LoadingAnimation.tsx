import { useEffect, useState } from 'react';
import { Player } from '@lottiefiles/react-lottie-player';
import { motion } from 'framer-motion';

interface LoadingAnimationProps {
  onComplete?: () => void;
}

const LoadingAnimation = ({ onComplete }: LoadingAnimationProps) => {
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    // Simulate minimum loading time
    const timer = setTimeout(() => {
      setIsComplete(true);
      onComplete?.();
    }, 2500);

    return () => clearTimeout(timer);
  }, [onComplete]);

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-background"
      initial={{ opacity: 1 }}
      animate={{
        opacity: isComplete ? 0 : 1,
        y: isComplete ? -50 : 0
      }}
      transition={{
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }}
      onAnimationComplete={() => {
        if (isComplete) {
          document.body.style.overflow = 'auto';
        }
      }}
    >
      <div className="flex flex-col items-center">
        <Player
          autoplay
          loop={false}
          src="/camera-shutter.json"
          style={{ height: '120px', width: '120px' }}
          onEvent={event => {
            if (event === 'complete') {
              // Animation has completed
            }
          }}
        />
        <motion.div
          className="mt-4 font-serif text-xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.8 }}
        >
          Chalaka Dulanga Photography
        </motion.div>
      </div>
    </motion.div>
  );
};

export default LoadingAnimation;
