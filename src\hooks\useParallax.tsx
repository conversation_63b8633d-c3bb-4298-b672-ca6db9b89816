
import { useEffect, useState } from 'react';

interface ParallaxOptions {
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  baseOffset?: number;
  responsive?: boolean;
}

export const useParallax = ({ 
  speed = 0.1, 
  direction = 'up',
  baseOffset = 0,
  responsive = false
}: ParallaxOptions = {}) => {
  const [offset, setOffset] = useState(0);
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });
  
  useEffect(() => {
    const handleResize = () => {
      if (responsive) {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }
    };
    
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const scrollX = window.scrollX;
      
      let newOffset = baseOffset;
      
      if (direction === 'up' || direction === 'down') {
        newOffset += scrollY * speed * (direction === 'up' ? -1 : 1);
      } else {
        newOffset += scrollX * speed * (direction === 'left' ? -1 : 1);
      }
      
      setOffset(newOffset);
    };
    
    window.addEventListener('scroll', handleScroll);
    if (responsive) {
      window.addEventListener('resize', handleResize);
    }
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (responsive) {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, [speed, direction, baseOffset, responsive]);
  
  return { offset, dimensions };
};

export default useParallax;
