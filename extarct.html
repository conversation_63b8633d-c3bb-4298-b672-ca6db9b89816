<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> | Premium Wedding & Event Photography Kandy</title> <!-- SEO Enhancement -->
    <script src="https://cdn.tailwindcss.com?plugins=forms"></script> <!-- Use Tailwind Play CDN for simplicity -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> <!-- Optional: Improves rendering, especially with CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* --- Modern Slate Palette --- */
            --color-primary: #1a1a1a;      /* Deep Charcoal/Near Black */
            --color-secondary: #475569;    /* Sophisticated Slate */
            --color-light: #F8FAFC;       /* Cool Light */
            --color-dark: #0F172A;        /* Deep Blue-Black */
            --color-text: #334155;        /* Slate Text */
            --color-text-light: #EAEAEA;  /* Light text on dark bg */
            --color-text-muted: #6c757d;  /* Slightly softer Muted Text (Bootstrap gray-600) */
            --color-border: #e0e0e0;      /* Subtle Border */
            --color-border-dark: #3F4759;    /* Slate Border */
            --color-light-alt: #E2E8F0;      /* Subtle Slate Light */
            --color-overlay: rgba(15, 23, 42, 0.6); /* Dark Slate Overlay */
            --color-overlay-light: rgba(71, 85, 105, 0.3); /* Light Slate Overlay */
        }

        html {
            scroll-behavior: smooth;
            scroll-padding-top: 90px; /* Adjust based on fixed navbar height */
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--color-text);
            background-color: var(--color-light);
            overflow-x: hidden;
            line-height: 1.7;
            font-weight: 400; /* Base weight */
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Playfair Display', serif;
            font-weight: 700; /* Bolder headings */
            line-height: 1.3;
            color: var(--color-primary);
            letter-spacing: 0.5px; /* Subtle spacing */
        }
        h1 { font-size: 3.5rem; /* Larger Base H1 */}
        h2 { font-size: 2.75rem; }
        h3 { font-size: 1.75rem; }
        h4 { font-size: 1.25rem; font-weight: 600; } /* Slightly lighter */

        @media (max-width: 767px) {
             h1 { font-size: 2.5rem; }
             h2 { font-size: 2rem; }
             h3 { font-size: 1.5rem; }
        }


        .font-heading { font-family: 'Playfair Display', serif; }
        .font-body { font-family: 'Poppins', sans-serif; }

        .text-primary { color: var(--color-primary); }
        .text-secondary { color: var(--color-secondary); }
        .text-light { color: var(--color-text-light); }
        .text-dark { color: var(--color-dark); }
        .text-muted { color: var(--color-text-muted); }

        .bg-primary { background-color: var(--color-primary); }
        .bg-secondary { background-color: var(--color-secondary); }
        .bg-light { background-color: var(--color-light); }
        .bg-dark { background-color: var(--color-dark); }
        .bg-light-alt { background-color: var(--color-light-alt); }

        .border-secondary { border-color: var(--color-secondary); }
        .border-light-custom { border-color: var(--color-border); }
        .border-dark-custom { border-color: var(--color-border-dark); }


        /* --- Premium Button Styles --- */
        .btn {
            display: inline-block;
            padding: 14px 38px; /* Slightly larger padding */
            font-weight: 600; /* Bolder */
            letter-spacing: 1px; /* More spacing */
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); /* Smoother transition */
            text-transform: uppercase;
            font-size: 13px; /* Smaller font, classic premium feel */
            border-radius: 4px; /* Slightly rounded */
            cursor: pointer;
            text-align: center;
            border: 2px solid transparent; /* Prepare for border transition */
        }

        .btn-primary {
            background-color: var(--color-secondary);
            color: #fff;
            border-color: var(--color-secondary);
        }

        .btn-primary:hover {
            background-color: var(--color-dark);
            color: white;
            border-color: var(--color-dark);
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--color-primary);
            border: 2px solid var(--color-border); /* Use subtle border */
        }
        .btn-outline:hover {
             background-color: var(--color-primary);
             border-color: var(--color-primary);
             color: var(--color-light);
             transform: translateY(-3px);
             box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
        }
         /* Secondary Outline Button */
         .btn-outline-secondary {
            background-color: transparent;
            color: var(--color-secondary);
            border: 2px solid var(--color-secondary);
         }
          .btn-outline-secondary:hover {
            background-color: var(--color-secondary);
            color: #fff;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(192, 168, 136, 0.2); /* Gold shadow */
          }

        /* --- Section Styling --- */
        .section-padding {
            padding-top: 7rem; /* Increased padding */
            padding-bottom: 7rem;
        }
        @media (min-width: 768px) {
            .section-padding {
                padding-top: 9rem;
                padding-bottom: 9rem;
            }
        }

        .container-xl {
            max-width: 1320px; /* Slightly wider container */
            margin-left: auto;
            margin-right: auto;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        /* --- Navbar Styling Refined --- */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            padding: 1.8rem 0; /* Increased initial padding */
            transition: all 0.4s ease-in-out;
            background-color: transparent;
        }

        .navbar.scrolled {
            background-color: rgba(26, 26, 26, 0.95); /* Darker, slightly transparent */
            padding: 1rem 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .navbar-brand {
            font-family: 'Playfair Display', serif;
            font-size: 2rem; /* Larger logo */
            font-weight: 800; /* Bolder */
            color: #fff; /* Initial color */
            transition: color 0.3s ease;
            letter-spacing: 1px;
        }

        /* Navbar brand color remains white when scrolled for dark background */
        /* .navbar.scrolled .navbar-brand { color: var(--color-primary); } */

        .nav-link {
            color: rgba(255, 255, 255, 0.8); /* Slightly muted white */
            font-weight: 500;
            font-size: 0.9rem; /* Slightly smaller nav links */
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 0 1.2rem; /* Increased spacing */
            position: relative;
            padding-bottom: 0.5rem; /* Space for underline */
            transition: color 0.3s ease;
        }
        /* Navbar links become brighter white on dark scrolled background */
        .navbar.scrolled .nav-link {
            color: var(--color-text-light);
        }
        .nav-link:hover, .nav-link.active { /* Added active state */
            color: #fff; /* Bright white on hover/active */
        }
        .nav-link::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%; /* Start from center */
            transform: translateX(-50%);
            width: 0;
            height: 2px; /* Thicker underline */
            background-color: var(--color-secondary);
            transition: width 0.4s ease-in-out;
        }
        .nav-link:hover::after, .nav-link.active::after {
            width: 70%; /* Underline doesn't span full width */
        }

        /* --- Hero Section Enhanced --- */
        .hero-section {
            height: 100vh;
            position: relative;
            overflow: hidden;
            color: #fff;
            display: flex;
            align-items: center;
        }
        .slideshow-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: opacity 0.8s ease-in-out;
        }
        .slide.active {
            opacity: 1;
        }

        /* Slideshow Navigation Styling */
         .slideshow-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.3);
            color: rgba(255,255,255,0.7);
            padding: 1rem 1.2rem;
            border: none;
            cursor: pointer;
            opacity: 0;
            transition: all 0.4s ease;
            z-index: 10;
            border-radius: 50%; /* Circular buttons */
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .slideshow-nav:hover {
            background: var(--color-secondary);
            color: white;
            transform: translateY(-50%) scale(1.1); /* Add subtle scale */
        }
        .slideshow-container:hover .slideshow-nav {
            opacity: 1;
        }
        .prev { left: 2rem; }
        .next { right: 2rem; }

        /* Slide Indicator Styling */
        .slide-indicators { /* No change */ }
        .indicator {
            width: 12px; /* Slightly larger */
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            border: 1px solid rgba(255,255,255,0.4); /* Add subtle border */
            cursor: pointer;
            transition: background-color 0.4s ease, transform 0.3s ease;
        }
        .indicator.active {
            background: var(--color-secondary);
            border-color: var(--color-secondary);
            transform: scale(1.2); /* Highlight active */
        }

        /* Hero Overlay */
        .hero-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, var(--color-overlay-light) 0%, var(--color-overlay) 100%); /* Darker gradient */
            z-index: 1; /* Ensure overlay is above slides but below content */
        }
        /* Hero Content Styling */
        .hero-content { /* Positioned via inline style z-10 */ }
        .hero-content h1 {
             color: #fff; /* Ensure heading is white */
             font-weight: 800; /* Bold */
             text-shadow: 0 2px 10px rgba(0,0,0,0.5); /* Text shadow for readability */
             margin-bottom: 1.5rem; /* More space */
        }
        .hero-content p {
            color: rgba(255, 255, 255, 0.9); /* Brighter paragraph */
            font-size: 1.25rem; /* Larger paragraph */
            max-width: 700px; /* Constrain width */
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 2.5rem; /* More space before button */
            text-shadow: 0 1px 5px rgba(0,0,0,0.4);
        }

        /* --- About Section Refined Layout --- */
        #about .grid {
            align-items: stretch; /* Make columns equal height if needed */
        }
         #about img {
             border-radius: 6px; /* Softer edges */
             box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15); /* Softer, larger shadow */
             height: 100%; /* Ensure image fills grid cell height */
             object-fit: cover;
         }
         #about .relative > div.absolute { /* Decorative element */
            border-color: var(--color-secondary);
            opacity: 0.5;
            width: 100px; height: 100px;
            bottom: -20px; left: -20px;
            border-width: 5px;
         }
         #about h2 {
            margin-bottom: 1.5rem; /* More space */
         }
         #about p {
            font-size: 1.05rem; /* Slightly larger body text */
         }
         #about .flex.items-center.space-x-3 { /* Stats section */
            background-color: var(--color-light-alt);
            padding: 1rem 1.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
         }
         #about .flex.items-center.space-x-3:hover {
             border-left-color: var(--color-secondary);
             background-color: #fff;
             box-shadow: 0 5px 15px rgba(0,0,0,0.05);
         }
         #about .flex.items-center i {
            color: var(--color-secondary); /* Icon color always secondary */
            transition: transform 0.3s ease;
         }
          #about .flex.items-center:hover i {
              transform: scale(1.1);
          }

        /* --- Service Card Enhanced --- */
        .service-card {
            background-color: #fff; /* White background for contrast */
            padding: 3rem; /* Increased padding */
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            border: 1px solid var(--color-border); /* Start with subtle border */
            border-radius: 6px; /* Slightly more rounded */
            text-align: center; /* Center align content */
            box-shadow: 0 5px 15px rgba(0,0,0,0.03); /* Subtle initial shadow */
        }
        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 45px rgba(0,0,0,0.1); /* More pronounced shadow on hover */
            border-color: var(--color-secondary); /* Highlight border on hover */
        }
        .service-card .fa-3x { /* Icon styling */
            font-size: 2.5rem; /* Slightly smaller */
            margin-bottom: 1.5rem;
            color: var(--color-secondary); /* Always use secondary color */
            transition: transform 0.4s ease;
        }
        .service-card:hover .fa-3x {
            transform: scale(1.1) rotate(-5deg); /* Add subtle rotation */
        }
        .service-card h4 {
            margin-bottom: 1rem; /* More space below title */
        }
        .service-card p {
            font-size: 0.95rem; /* Standard size */
            color: var(--color-text-muted); /* Muted color */
        }
        .service-card ul {
            margin-top: 1.5rem;
            padding-left: 0; /* Remove default padding */
            list-style: none; /* Remove bullets */
            text-align: left; /* Align list items left */
            font-size: 0.9rem;
        }
        .service-card ul li {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.2rem; /* Space for custom icon */
        }
         .service-card ul li::before {
            content: "\f00c"; /* FontAwesome check mark */
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            color: var(--color-secondary);
            position: absolute;
            left: 0;
            top: 2px; /* Adjust vertical alignment */
            font-size: 0.8rem;
         }

        /* --- Portfolio Section Reimagined --- */
        #portfolio .text-center h2 { margin-bottom: 1rem; }
        #portfolio .text-center p { margin-bottom: 3rem; } /* More space */

        /* Removed filter buttons styles as they are removed in HTML */

        /* Portfolio Grid & Items */
        .portfolio-grid { /* Use this class on the grid container */
            /* display: grid; Use Tailwind grid classes */
            gap: 2rem; /* Increased gap */
        }
        .portfolio-couple-heading { /* Style for the couple names */
             font-family: 'Poppins', sans-serif; /* Use body font */
             font-weight: 600;
             text-align: left;
             margin-top: 4rem; /* Space above heading */
             margin-bottom: 1.5rem; /* Space below heading */
             padding-bottom: 0.8rem;
             border-bottom: 1px solid var(--color-border);
             font-size: 1.3rem;
             color: var(--color-text-muted);
             letter-spacing: 1px;
             text-transform: uppercase;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 6px; /* Consistent rounding */
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); /* Subtle shadow */
            transition: transform 0.4s ease, box-shadow 0.4s ease;
        }
        .gallery-item:hover {
             transform: translateY(-5px);
             box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        .gallery-item img {
            display: block;
            width: 100%;
            height: 100%; /* Ensure image fills container */
            object-fit: cover;
            transition: transform 0.7s cubic-bezier(0.25, 0.8, 0.25, 1); /* Smoother, longer zoom */
        }
         .gallery-item:hover img {
            transform: scale(1.08); /* Slightly larger scale on hover */
         }

        /* Removed gallery overlay styles */

        /* Portfolio CTA Button */
         #portfolio .text-center.mt-12 .btn {
            margin-top: 2rem; /* More space above button */
         }

        /* --- Testimonial Card Refined --- */
        .testimonial-card {
            background-color: transparent; /* No background */
            padding: 2rem 0;
            text-align: center;
            position: relative;
            border-bottom: 1px solid var(--color-border); /* Separator */
            margin-bottom: 2rem;
        }
        /* Remove avatar images */
        /* .testimonial-card img { ... } */

        .testimonial-card blockquote {
            font-family: 'Playfair Display', serif; /* Elegant quote font */
            font-style: italic;
            font-size: 1.5rem; /* Larger quote */
            line-height: 1.6;
            color: var(--color-primary); /* Darker quote */
            margin-bottom: 1.5rem;
            position: relative;
            padding: 0 2rem; /* Indent quote */
        }
        .testimonial-card blockquote::before { /* Stylistic quote marks */
            content: '“';
            font-family: 'Playfair Display', serif;
            font-size: 5rem;
            color: var(--color-secondary);
            opacity: 0.2;
            position: absolute;
            left: -0.5rem;
            top: -1rem;
            line-height: 1;
        }
        .testimonial-card .mt-4 h5 { /* Name */
             font-family: 'Poppins', sans-serif;
             font-weight: 600;
             font-size: 1rem;
             text-transform: uppercase;
             letter-spacing: 1px;
             margin-bottom: 0.2rem;
        }
        .testimonial-card .mt-4 p { /* Service type */
            font-size: 0.9rem;
            color: var(--color-text-muted);
        }
        /* Hide the decorative quote icon */
        .testimonial-card .text-secondary.text-4xl.mt-4.opacity-50 {
            display: none;
        }

        /* --- Booking Form Premium Styling --- */
        .booking-form-container {
            background-color: #fff; /* White background */
            padding: 3rem 2.5rem; /* Adjust padding */
            box-shadow: 0 15px 50px rgba(0,0,0,0.1); /* Enhanced shadow */
            border-radius: 8px; /* Softer radius */
            border: 1px solid var(--color-border);
        }
         @media (min-width: 768px) {
             .booking-form-container {
                 padding: 4rem 3.5rem; /* More padding on larger screens */
             }
         }

        /* Form Inputs Enhanced */
        .form-input {
            display: block;
            width: 100%;
            padding: 1rem 1.25rem; /* Larger padding */
            border: 1px solid var(--color-border); /* Use standard border */
            border-radius: 4px; /* Consistent radius */
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            font-size: 1rem;
            color: var(--color-text);
            background-color: var(--color-light); /* Light background for inputs */
        }
        .form-input:focus {
            outline: none;
            border-color: var(--color-secondary);
            box-shadow: 0 0 0 3px rgba(192, 168, 136, 0.2); /* Softer focus ring matching secondary */
            background-color: #fff; /* White background on focus */
        }
        .form-label {
            display: block;
            margin-bottom: 0.6rem; /* More space */
            font-weight: 600; /* Bolder labels */
            font-size: 0.875rem;
            color: var(--color-primary);
        }
        /* Select Wrapper Arrow */
        .select-wrapper::after {
            content: "\f078"; /* FontAwesome Angle Down */
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            position: absolute;
            right: 1.25rem; /* Match input padding */
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            color: var(--color-text-muted); /* Muted arrow */
            font-size: 0.8rem;
            transition: color 0.3s ease;
        }
        .select-wrapper:hover::after {
            color: var(--color-secondary); /* Color change on hover */
        }
        .select-input {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            padding-right: 3.5rem; /* More space for arrow */
        }
        /* Textarea */
        textarea.form-input {
             min-height: 120px; /* Set min height */
        }

        /* Step Indicator Refined */
        .step-indicator {
            display: flex;
            justify-content: space-around; /* Space out better */
            margin-bottom: 3rem; /* More space */
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--color-border);
            gap: 1rem; /* Add gap for wrapping */
        }
        .step {
            text-align: center;
            flex: 1;
            min-width: 80px; /* Ensure minimum width */
            position: relative;
            color: var(--color-text-muted); /* Muted by default */
            transition: color 0.4s ease;
            cursor: default; /* Not clickable */
        }
        .step-number {
            width: 36px; /* Larger circle */
            height: 36px;
            border-radius: 50%;
            border: 2px solid var(--color-border); /* Thicker border */
            color: var(--color-text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.8rem; /* More space below */
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.4s ease;
            background-color: var(--color-light); /* Light background */
        }
        .step.active .step-number {
            background-color: var(--color-secondary);
            color: #fff;
            border-color: var(--color-secondary);
            transform: scale(1.1); /* Scale up active step */
            box-shadow: 0 0 15px rgba(192, 168, 136, 0.4); /* Glow effect */
        }
        .step.active {
            color: var(--color-primary); /* Darker text for active step */
            font-weight: 600;
        }
        .step-title {
            font-size: 0.8rem; /* Smaller title */
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-section { display: none; }
        .form-section.active { display: block; animation: fadeIn 0.5s ease forwards; }

        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }

        /* Event Section Styling within Form */
         .event-section {
            border-top: 1px dashed var(--color-border); /* Dashed separator */
            padding-top: 2rem;
            margin-top: 2rem; /* Space above separator */
         }
         .event-section h4 {
            color: var(--color-secondary); /* Highlight section titles */
            margin-bottom: 1.5rem;
            font-weight: 700;
         }

        /* Booking Summary Styling */
         #bookingSummary {
            background-color: var(--color-light-alt); /* Use alt light bg */
            padding: 1.5rem 2rem;
            border: 1px solid var(--color-border);
            border-radius: 6px;
            font-size: 0.95rem;
         }
         #bookingSummary p { margin-bottom: 0.6rem; }
         #bookingSummary strong { font-weight: 600; color: var(--color-primary); }
         #bookingSummary hr { border-color: var(--color-border); margin: 1rem 0; }

        /* Terms Checkbox */
         .form-checkbox:checked {
            border-color: var(--color-secondary);
            background-color: var(--color-secondary);
         }
         .form-checkbox:focus {
             box-shadow: 0 0 0 3px rgba(192, 168, 136, 0.3);
             border-color: var(--color-secondary);
         }
         #termsCheckbox + span a { /* Link style */
             color: var(--color-secondary);
             font-weight: 600;
             text-decoration: none;
         }
          #termsCheckbox + span a:hover {
             text-decoration: underline;
          }

        /* --- Invoice Styling --- */
        .invoice {
            background-color: #fff;
            padding: 3rem; /* Generous padding */
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            border: 1px solid var(--color-border);
            border-radius: 8px; /* Consistent radius */
            font-size: 0.95rem; /* Base font size */
        }
         @media (min-width: 768px) {
             .invoice { padding: 4rem; }
         }
        .invoice-logo {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem; /* Larger */
            font-weight: 800; /* Bolder */
            color: var(--color-primary);
            margin-bottom: 1rem; /* Add space */
        }
        .invoice .text-sm { font-size: 0.9rem; } /* Ensure consistent sm size */
        .invoice h5 { /* Subheadings */
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            color: var(--color-text-muted);
            margin-bottom: 0.8rem;
        }
        .invoice-table th {
            background-color: var(--color-light-alt);
            text-align: left;
            padding: 1rem 1.25rem; /* More padding */
            font-weight: 600;
            font-size: 0.8rem; /* Small caps */
            text-transform: uppercase;
            letter-spacing: 0.8px;
            color: var(--color-primary);
            border-bottom: 2px solid var(--color-border); /* Stronger bottom border */
        }
        .invoice-table td {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid var(--color-border);
            vertical-align: top; /* Align top */
        }
         .invoice-table tr:last-child td { border-bottom: none; }
         .invoice-table tfoot td { font-weight: 600; }
         .invoice-table tfoot strong { font-weight: 700; }
         .invoice-table tfoot tr { background-color: var(--color-light-alt); border-top: 2px solid var(--color-border); }

        .invoice-footer {
            margin-top: 3rem;
            text-align: center;
            color: var(--color-text-muted);
            font-size: 0.85rem;
            border-top: 1px dashed var(--color-border);
            padding-top: 2rem;
        }
        #invoice .btn { margin: 0 0.5rem; } /* Space out buttons */

        /* --- Contact Section Styling --- */
        #contact .text-center h2 { margin-bottom: 1rem; }
        #contact .text-center p { margin-bottom: 3rem; }
        #contact h4 { /* Send Message / Contact Info headings */
             margin-bottom: 2rem;
             color: var(--color-primary);
        }
        /* Contact Info Styling */
         #contact .flex.items-start.space-x-4 { /* Each info item */
            margin-bottom: 1.5rem;
         }
         #contact .flex.items-start i {
            color: var(--color-secondary);
            font-size: 1.2rem; /* Slightly larger icons */
            width: 25px; /* Ensure alignment */
            text-align: center;
            margin-top: 0.2rem;
         }
         #contact .flex.items-start h5 { /* Info title */
             font-family: 'Poppins', sans-serif;
             font-weight: 600;
             margin-bottom: 0.3rem;
             font-size: 0.9rem;
             text-transform: uppercase;
             letter-spacing: 0.5px;
         }
         #contact .flex.items-start p,
         #contact .flex.items-start a {
             color: var(--color-text-muted);
             font-size: 0.95rem;
             text-decoration: none;
             transition: color 0.3s ease;
         }
         #contact .flex.items-start a:hover {
             color: var(--color-secondary);
         }
         /* Social Icons in Contact */
         #contact .flex.space-x-4 a {
            color: var(--color-text-muted);
            font-size: 1.3rem;
            transition: color 0.3s ease, transform 0.3s ease;
         }
         #contact .flex.space-x-4 a:hover {
            color: var(--color-secondary);
            transform: scale(1.1);
         }
        /* Contact Form uses .form-input, .form-label styles defined earlier */
        #contactForm button[type="submit"] {
             margin-top: 1rem; /* Space above submit button */
        }


        /* --- Footer Enhanced --- */
        footer {
            background-color: var(--color-dark);
            color: var(--color-text-light);
            padding-top: 5rem; /* More padding */
            padding-bottom: 3rem;
            font-size: 0.9rem;
        }
        footer h5 {
            font-family: 'Poppins', sans-serif; /* Consistent font */
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1.5rem;
            color: #fff; /* White headings */
        }
        footer p, footer li, footer a {
            color: rgba(255, 255, 255, 0.7); /* Muted light text */
            transition: color 0.3s ease;
        }
        footer a:hover {
            color: var(--color-secondary); /* Gold hover */
        }
        footer .flex.space-x-4 a { /* Footer social icons */
            font-size: 1.1rem;
             color: rgba(255, 255, 255, 0.7);
        }
        footer .flex.space-x-4 a:hover {
            color: var(--color-secondary);
            transform: scale(1.1);
        }
        footer ul { list-style: none; padding: 0; }
        footer ul li { margin-bottom: 0.8rem; }
        footer ul i { margin-right: 0.7rem; width: 18px; text-align: center; color: var(--color-secondary); } /* Icon color & spacing */
        footer hr { border-color: var(--color-border-dark); margin: 3rem 0; } /* Darker border */
        footer .text-center p {
            color: rgba(255, 255, 255, 0.5); /* Very muted copyright */
            font-size: 0.85rem;
        }

        /* --- Fade In Animation (Subtler) --- */
        .fade-in {
            opacity: 0;
            transform: translateY(30px); /* Slightly more distance */
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .fade-in.appear {
            opacity: 1;
            transform: translateY(0);
        }


        /* --- Responsive Adjustments --- */
         @media (max-width: 991px) { /* Below lg breakpoint - Mobile Menu */
             .navbar-toggler {
                 border: none;
                 padding: 0.5rem;
                 color: #fff; /* Always white */
                 z-index: 1001; /* Above menu */
                 transition: color 0.3s ease;
             }
             /* .navbar.scrolled .navbar-toggler remains white */

             .navbar-collapse { /* Mobile Menu Panel */
                 position: fixed;
                 top: 0;
                 left: -100%;
                 width: 85%; /* Wider */
                 max-width: 350px;
                 height: 100vh;
                 background-color: var(--color-dark); /* Use dark bg */
                 padding: 7rem 2.5rem 2rem; /* Adjust padding */
                 box-shadow: 5px 0 25px rgba(0,0,0,0.2);
                 transition: left 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
                 overflow-y: auto;
                 z-index: 1000;
             }
             .navbar-collapse.show {
                 left: 0;
             }
             .navbar-nav {
                 flex-direction: column;
                 align-items: flex-start;
             }
             .nav-item { width: 100%; }
             .nav-link { /* Mobile Menu Links */
                 color: var(--color-text-light); /* Light text on dark bg */
                 margin: 0.5rem 0; /* Adjust vertical spacing */
                 padding: 0.8rem 0;
                 width: 100%;
                 display: block;
                 font-size: 1rem; /* Larger font size */
                 text-transform: uppercase;
                 letter-spacing: 1px;
             }
             .nav-link::after { display: none; } /* Remove underline */
             .nav-link:hover, .nav-link.active {
                color: var(--color-secondary); /* Highlight color */
             }
         }

         @media (max-width: 767px) {
            /* General smaller screen adjustments */
            .section-padding { padding-top: 5rem; padding-bottom: 5rem; }
            .hero-section { height: 90vh; } /* Slightly shorter */
            .hero-content p { font-size: 1.1rem; }
            .btn { padding: 12px 30px; font-size: 12px; }
            .slideshow-nav { width: 40px; height: 40px; padding: 0.8rem; }
            .prev { left: 1rem; } .next { right: 1rem; }
            .indicator { width: 10px; height: 10px; }

            /* Portfolio grid adjustments if needed */
            .portfolio-grid { gap: 1.5rem; }

            /* Booking form steps wrap */
            .step-indicator { flex-wrap: wrap; justify-content: center; gap: 1.5rem; }
            .step { flex-basis: 40%; /* Approx 2 per row */ min-width: 60px; }

             /* Invoice padding */
             .invoice { padding: 2rem; }
             .invoice-table th, .invoice-table td { padding: 0.8rem 1rem; }

             /* Contact section layout */
             #contact .lg\:col-span-2, #contact .lg\:col-span-3 { grid-column: span 1 / span 1; } /* Stack columns */
             #contact .grid { gap: 3rem; } /* Add gap between stacked columns */

            /* Footer layout */
             footer .grid { grid-template-columns: 1fr; text-align: center; }
             footer .lg\:col-span-2 { grid-column: span 1 / span 1; }
             footer .flex.space-x-4 { justify-content: center; }
             footer ul { text-align: center; }
             footer ul i { display: none; } /* Hide icons in list on mobile */

         }


    </style>
</head>
<body class="font-body antialiased"> <!-- Added antialiased for smoother fonts -->

    <!-- Navigation -->
    <nav id="mainNav" class="navbar">
        <div class="container-xl flex justify-between items-center">
            <a class="navbar-brand" href="#home">Chalaka Dulanga</a>
            <!-- Mobile Menu Button -->
            <button id="navbarToggler" class="lg:hidden navbar-toggler focus:outline-none" title="Toggle navigation menu">
                <!-- Icon will be toggled via JS -->
                <i class="fas fa-bars text-2xl" aria-hidden="true"></i>
                <span class="sr-only">Toggle Menu</span>
            </button>
            <!-- Desktop Menu -->
            <div id="navbarNav" class="hidden lg:flex lg:items-center">
                <!-- Links remain the same -->
                 <ul class="flex flex-col lg:flex-row list-none lg:ml-auto">
                     <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                     <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                     <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
                     <li class="nav-item"><a class="nav-link" href="#portfolio">Portfolio</a></li>
                     <li class="nav-item"><a class="nav-link" href="#testimonials">Testimonials</a></li>
                     <li class="nav-item"><a class="nav-link" href="#booking">Book Now</a></li>
                     <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                 </ul>
            </div>
        </div>
         <!-- Mobile Menu (Panel) -->
         <div id="mobileMenu" class="navbar-collapse lg:hidden">
             <!-- Links remain the same -->
             <ul class="navbar-nav">
                 <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                 <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                 <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
                 <li class="nav-item"><a class="nav-link" href="#portfolio">Portfolio</a></li>
                 <li class="nav-item"><a class="nav-link" href="#testimonials">Testimonials</a></li>
                 <li class="nav-item"><a class="nav-link" href="#booking">Book Now</a></li>
                 <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
             </ul>
         </div>
    </nav>


    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="slideshow-container">
            <!-- Slideshow images remain the same -->
            <div class="slide fade-slide active" style="background-image: url('Shehani & Dharshana/484790846_964656452532432_53661832893256839_n.jpg');"></div>
            <div class="slide fade-slide" style="background-image: url('Natasha & Ramitha/487407218_973487338316010_3795640447878046813_n.jpg');"></div>
            <div class="slide fade-slide" style="background-image: url('Asiri & Shrimali/487316291_973336151664462_4540413166843432055_n.jpg');"></div>
            <div class="slide fade-slide" style="background-image: url('Nilakshi & Charith/485627792_965623082435769_2730115173270551094_n.jpg');"></div>
            <!-- Slideshow navigation remains the same -->
            <button class="slideshow-nav prev" aria-label="Previous slide"><i class="fas fa-chevron-left"></i></button>
            <button class="slideshow-nav next" aria-label="Next slide"><i class="fas fa-chevron-right"></i></button>
            <!-- Slide indicators remain the same -->
             <div class="slide-indicators">
                 <span class="indicator active" data-slide-index="0"></span>
                 <span class="indicator" data-slide-index="1"></span>
                 <span class="indicator" data-slide-index="2"></span>
                 <span class="indicator" data-slide-index="3"></span>
             </div>
        </div>
        <!-- Hero Content (z-index added inline) -->
        <div class="container-xl relative z-10">
            <div class="flex justify-center text-center">
                <div class="w-full lg:w-5/6 hero-content"> <!-- Slightly wider content area -->
                    <h1 class="font-heading font-bold mb-6 fade-in">Transforming Moments Into Lifelong Treasures</h1>
                    <p class="mb-10 fade-in" style="animation-delay: 0.2s;">With an artistic eye and a passion for storytelling, I capture the joy, beauty, and authentic emotion of your most precious occasions.</p>
                    <a href="#booking" class="btn btn-primary fade-in" style="animation-delay: 0.4s;">Book Your Session</a>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section-padding">
        <div class="container-xl">
            <!-- Structure remains the same, styling handled by CSS -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center"> <!-- Increased gap -->
                <div class="relative fade-in">
                    <img src="Chalaka Dulanga.jpg" alt="Chalaka Dulanga Photographer" class="rounded-md shadow-xl w-full object-cover aspect-[4/5]"> <!-- Adjusted aspect ratio slightly -->
                    <div class="absolute -bottom-5 -left-5 w-24 h-24 border-4 border-secondary opacity-40 rounded-md hidden md:block z-[-1]"></div> <!-- Adjusted style -->
                </div>
                <div class="fade-in lg:pl-8" style="animation-delay: 0.2s;"> <!-- Added left padding on large screens -->
                    <h2 class="font-heading mb-6">Meet Chalaka Dulanga</h2>
                    <p class="text-lg mb-5 text-muted">Your dedicated photographer based in the heart of Kandy, Sri Lanka, specializing in crafting timeless visual narratives.</p>
                    <p class="mb-5">My passion lies in capturing the essence of weddings, engagements, pre-shoots, and homecomings. I blend candid, documentary storytelling with elegant, artistic portraiture, ensuring every image resonates with genuine emotion and beauty.</p>
                    <p class="mb-8">With over five years of experience, my approach is relaxed and guided, creating a comfortable atmosphere where your unique story unfolds naturally, resulting in photographs you'll treasure forever.</p>
                    <!-- Stats/Features Section - structure remains, CSS styles it -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-10">
                         <div class="flex items-center space-x-4">
                             <i class="fas fa-camera-retro fa-2x text-secondary"></i>
                             <div>
                                 <h5 class="font-semibold text-base">Artistic Vision</h5>
                                 <p class="text-sm text-muted">Capturing unique perspectives</p>
                             </div>
                         </div>
                         <div class="flex items-center space-x-4">
                             <i class="fas fa-heart-pulse fa-2x text-secondary"></i> <!-- Changed icon -->
                             <div>
                                 <h5 class="font-semibold text-base">Genuine Emotion</h5>
                                 <p class="text-sm text-muted">Focusing on authentic moments</p>
                             </div>
                         </div>
                         <div class="flex items-center space-x-4">
                             <i class="far fa-clock fa-2x text-secondary"></i> <!-- Changed icon -->
                             <div>
                                 <h5 class="font-semibold text-base">Timeless Elegance</h5>
                                 <p class="text-sm text-muted">Classic and enduring style</p>
                             </div>
                         </div>
                         <div class="flex items-center space-x-4">
                             <i class="fas fa-award fa-2x text-secondary"></i> <!-- Changed icon -->
                             <div>
                                 <h5 class="font-semibold text-base">Premium Experience</h5>
                                 <p class="text-sm text-muted">Quality service & results</p>
                             </div>
                         </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="section-padding bg-light-alt">
        <div class="container-xl">
            <div class="text-center mb-20"> <!-- Increased margin bottom -->
                <h2 class="font-heading mb-4">Capturing Your Story</h2>
                <p class="text-lg text-muted max-w-3xl mx-auto mb-8">Offering a comprehensive suite of photography and cinematography services designed to beautifully document every chapter of your celebration.</p>
                 <div class="flex justify-center">
                     <a href="Chalaka-Dulanga-Photography-Packages.pdf" class="btn btn-outline-secondary" target="_blank"> <!-- Changed button style -->
                         <i class="fas fa-file-alt mr-2"></i>View Detailed Packages <!-- Changed icon -->
                     </a>
                 </div>
            </div>
             <!-- Grid structure remains, cards styled by CSS -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                 <!-- Service Card 1: Wedding -->
                 <div class="service-card fade-in">
                     <i class="fas fa-ring fa-3x"></i>
                     <h4 class="font-heading text-xl mb-3">Wedding Photography</h4>
                     <p class="text-muted text-sm mb-4">Documenting the magic of your wedding day, from the intimate preparations to the joyous celebrations, with artistry and care.</p>
                     <ul class="mt-4 space-y-1">
                         <li>Full-day & Half-day Coverage</li>
                         <li>Optional Second Photographer</li>
                         <li>Exquisite Heirloom Albums</li>
                         <li>Online Gallery & Digital Files</li>
                     </ul>
                 </div>
                 <!-- Service Card 2: Engagement -->
                 <div class="service-card fade-in" style="animation-delay: 0.1s;">
                     <i class="fas fa-heart fa-3x"></i>
                     <h4 class="font-heading text-xl mb-3">Engagement Sessions</h4>
                     <p class="text-muted text-sm mb-4">Celebrate your unique connection with a relaxed and romantic photoshoot that tells the story of your love before the big day.</p>
                      <ul class="mt-4 space-y-1">
                         <li>Choice of Scenic Locations</li>
                         <li>Personalized Style Guidance</li>
                         <li>Natural & Posed Portraits</li>
                         <li>Images for Save-the-Dates</li>
                     </ul>
                 </div>
                 <!-- Service Card 3: Cinematography -->
                  <div class="service-card fade-in" style="animation-delay: 0.2s;">
                     <i class="fas fa-film fa-3x"></i> <!-- Changed icon -->
                     <h4 class="font-heading text-xl mb-3">Cinematic Films</h4>
                     <p class="text-muted text-sm mb-4">Crafting beautiful, emotive wedding films that capture the movement, sounds, and emotions of your day like a timeless movie.</p>
                      <ul class="mt-4 space-y-1">
                         <li>Highlight Reels (3-5 min)</li>
                         <li>Feature Films (15-25 min)</li>
                         <li>Aerial Drone Footage</li>
                         <li>Professional Audio Recording</li>
                     </ul>
                 </div>
                  <!-- Service Card 4: Homecoming -->
                  <div class="service-card fade-in" style="animation-delay: 0.3s;">
                     <i class="fas fa-glass-cheers fa-3x"></i> <!-- Changed icon -->
                     <h4 class="font-heading text-xl mb-3">Homecoming Celebrations</h4>
                     <p class="text-muted text-sm mb-4">Capturing the warmth and joy of your homecoming event as you celebrate the beginning of your life together with loved ones.</p>
                      <ul class="mt-4 space-y-1">
                         <li>Event Coverage</li>
                         <li>Family & Group Portraits</li>
                         <li>Candid Moment Capture</li>
                         <li>Optional Custom Albums</li>
                     </ul>
                 </div>
                  <!-- Service Card 5: Pre-Wedding -->
                  <div class="service-card fade-in" style="animation-delay: 0.4s;">
                     <i class="fas fa-map-marked-alt fa-3x"></i> <!-- Changed icon -->
                     <h4 class="font-heading text-xl mb-3">Pre-Wedding Shoots</h4>
                     <p class="text-muted text-sm mb-4">An adventurous and creative photoshoot experience in stunning locations, resulting in breathtaking, magazine-worthy images.</p>
                      <ul class="mt-4 space-y-1">
                         <li>Location Scouting & Planning</li>
                         <li>Themed & Conceptual Shoots</li>
                         <li>Multiple Outfit Coordination</li>
                         <li>Ideal for Display Prints</li>
                     </ul>
                 </div>
                  <!-- Service Card 6: Combo -->
                  <div class="service-card fade-in" style="animation-delay: 0.5s;">
                     <i class="fas fa-layer-group fa-3x"></i> <!-- Changed icon -->
                     <h4 class="font-heading text-xl mb-3">Combined Packages</h4>
                     <p class="text-muted text-sm mb-4">Comprehensive packages offering seamless coverage across multiple events (Wedding, Homecoming, Pre-shoot) at preferential rates.</p>
                      <ul class="mt-4 space-y-1">
                         <li>Wedding + Homecoming</li>
                         <li>Engagement + Wedding</li>
                         <li>Pre-Shoot + Wedding + HC</li>
                         <li>Fully Customizable Options</li>
                     </ul>
                 </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="section-padding">
        <div class="container-xl">
            <div class="text-center mb-16">
                <h2 class="font-heading mb-4">Featured Stories</h2>
                <p class="text-lg text-muted max-w-3xl mx-auto">A curated selection of moments and emotions from recent celebrations, showcasing the beauty and joy I strive to capture.</p>
            </div>

            <!-- Portfolio Grid - Using new CSS class for potential styling -->
            <div class="portfolio-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Couple Name Headings Replaced with more subtle class -->
                <h3 class="portfolio-couple-heading col-span-1 sm:col-span-2 lg:col-span-3">Nilakshi & Charith</h3>
                 <div class="gallery-item fade-in">
                     <img src="Nilakshi & Charith/485150020_965624465768964_3615356717551209126_n.jpg" alt="Nilakshi & Charith Wedding Portrait" class="w-full aspect-[4/5] object-cover">
                 </div>
                 <div class="gallery-item fade-in">
                     <img src="Nilakshi & Charith/485620673_965623275769083_6787835819479725019_n.jpg" alt="Nilakshi & Charith Wedding Ceremony" class="w-full aspect-[4/5] object-cover">
                 </div>
                 <div class="gallery-item fade-in">
                     <img src="Nilakshi & Charith/485765335_965624385768972_1614957334475339065_n.jpg" alt="Nilakshi & Charith Wedding Details" class="w-full aspect-[4/5] object-cover">
                 </div>



                 <h3 class="portfolio-couple-heading col-span-1 sm:col-span-2 lg:col-span-3">Sandamini & Akalanka</h3>
                 <div class="gallery-item fade-in">
                     <img src="Sandamini & Akalanka  Pre Shoot/480172595_933140129028257_6839390153575495126_n.jpg" alt="Sandamini & Akalanka Pre-Wedding Shoot" class="w-full aspect-[4/5] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Sandamini & Akalanka  Pre Shoot/480208364_933140085694928_3182932142754319618_n.jpg" alt="Sandamini & Akalanka Pre-Wedding Nature" class="w-full aspect-[4/5] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Sandamini & Akalanka  Pre Shoot/480211747_933139155695021_2667868682799558144_n.jpg" alt="Sandamini & Akalanka Pre-Wedding Portrait" class="w-full aspect-[4/5] object-cover">
                 </div>

                 <h3 class="portfolio-couple-heading col-span-1 sm:col-span-2 lg:col-span-3">Asiri & Shrimali</h3>
                 <div class="gallery-item fade-in">
                    <img src="Asiri & Shrimali/487287125_973335398331204_5668565061624362055_n.jpg" alt="Asiri & Shrimali Wedding Candid Moment" class="w-full aspect-[3/4] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Asiri & Shrimali/487453666_973335261664551_2482826657557006409_n.jpg" alt="Asiri & Shrimali Wedding Outdoor Portrait" class="w-full aspect-[3/4] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Asiri & Shrimali/487778407_973334071664670_3687798998804228316_n.jpg" alt="Asiri & Shrimali Wedding Couple Shot" class="w-full aspect-[3/4] object-cover">
                 </div>

                 <h3 class="portfolio-couple-heading col-span-1 sm:col-span-2 lg:col-span-3">Natasha & Ramitha</h3>
                 <div class="gallery-item fade-in">
                     <img src="Natasha & Ramitha/488054893_973487564982654_6240773872504711993_n.jpg" alt="Natasha & Ramitha Wedding Portrait" class="w-full aspect-[4/5] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Natasha & Ramitha/487313407_973486634982747_921204117200231749_n.jpg" alt="Natasha & Ramitha Wedding Black and White" class="w-full aspect-[4/5] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Natasha & Ramitha/487807876_973487594982651_8216548130856014086_n.jpg" alt="Natasha & Ramitha Wedding Reception" class="w-full aspect-[4/5] object-cover">
                 </div>

                 <h3 class="portfolio-couple-heading col-span-1 sm:col-span-2 lg:col-span-3">Dhana & Thilan</h3>
                 <div class="gallery-item fade-in">
                     <img src="Dhana & Thilan/481177125_951891433808934_2138158755566595553_n.jpg" alt="Dhana & Thilan Wedding Portrait Close Up" class="w-full aspect-[3/4] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Dhana & Thilan/480924989_951891237142287_8269153790287534980_n.jpg" alt="Dhana & Thilan Wedding Groom Detail" class="w-full aspect-[3/4] object-cover">
                 </div>
                  <div class="gallery-item fade-in">
                     <img src="Dhana & Thilan/481259743_951891450475599_5227627150470168132_n.jpg" alt="Dhana & Thilan Wedding Bride Detail" class="w-full aspect-[3/4] object-cover">
                 </div>

            </div>

            <div class="text-center mt-16"> <!-- Increased margin -->
                <a href="https://www.facebook.com/chalakadulangaphotography/photos_albums" target="_blank" rel="noopener noreferrer" class="btn btn-primary">Explore Full Galleries</a> <!-- Changed wording -->
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="section-padding bg-light-alt">
        <div class="container-xl">
            <div class="text-center mb-16">
                <h2 class="font-heading mb-4">Words of Appreciation</h2> <!-- Changed heading -->
                <p class="text-lg text-muted max-w-3xl mx-auto">Hear from couples who entrusted me with their precious memories.</p>
            </div>
             <!-- Structure remains, styling handled by CSS -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
                 <!-- Testimonial 1 -->
                 <div class="testimonial-card fade-in">
                     <blockquote class="mb-4">
                          "Choosing Chalaka was the best decision! He captured the essence of our day perfectly. The photos are beyond beautiful, filled with emotion and artistry. We felt so comfortable throughout."
                     </blockquote>
                      <div class="mt-4">
                         <h5 class="font-semibold">Dinithi & Izam</h5>
                         <p class="text-sm text-muted">Wedding Photography</p>
                     </div>
                     <!-- Removed quote icon div -->
                 </div>
                  <!-- Testimonial 2 -->
                 <div class="testimonial-card fade-in" style="animation-delay: 0.2s;">
                      <blockquote class="mb-4">
                         "Our engagement photos are stunning! Chalaka has an amazing talent for making you feel relaxed and capturing natural, joyful moments. We absolutely love them. Highly recommended!"
                     </blockquote>
                      <div class="mt-4">
                         <h5 class="font-semibold">Nikini & Devin</h5>
                         <p class="text-sm text-muted">Engagement Session</p>
                     </div>
                      <!-- Removed quote icon div -->
                 </div>
                  <!-- Testimonial 3 -->
                 <div class="testimonial-card fade-in" style="animation-delay: 0.4s;">
                     <blockquote class="mb-4">
                         "Chalaka covered both our wedding and homecoming flawlessly. The photos tell such a beautiful story. His professionalism and creative eye are truly exceptional. Thank you!"
                     </blockquote>
                      <div class="mt-4">
                         <h5 class="font-semibold">Sharon & Gayas</h5>
                         <p class="text-sm text-muted">Wedding & Homecoming</p>
                     </div>
                      <!-- Removed quote icon div -->
                 </div>
            </div>
        </div>
    </section>

     <!-- Booking Section -->
    <section id="booking" class="section-padding">
        <div class="container-xl">
            <div class="text-center mb-16">
                <h2 class="font-heading mb-4">Begin Your Story</h2> <!-- Changed heading -->
                <p class="text-lg text-muted max-w-3xl mx-auto">Ready to discuss how I can capture your special day? Please fill out the booking inquiry form below to check availability and get started.</p>
            </div>
            <div class="max-w-4xl mx-auto">
                 <!-- Form container structure remains, styled by CSS -->
                <div class="booking-form-container">
                    <!-- Step Indicator structure remains, styled by CSS -->
                    <div class="step-indicator">
                         <div class="step active" data-step="1">
                             <div class="step-number">1</div>
                             <div class="step-title">Event</div>
                         </div>
                         <div class="step" data-step="2">
                             <div class="step-number">2</div>
                             <div class="step-title">Details</div>
                         </div>
                         <div class="step" data-step="3">
                             <div class="step-number">3</div>
                             <div class="step-title">Contact</div>
                         </div>
                         <div class="step" data-step="4">
                             <div class="step-number">4</div>
                             <div class="step-title">Review</div>
                         </div>
                     </div>

                    <!-- Form structure remains the same, inputs/buttons styled by CSS -->
                    <form id="bookingForm" class="space-y-6">
                        <!-- Step 1: Event Type -->
                         <div class="form-section active" data-step="1">
                             <div>
                                 <label for="eventType" class="form-label">Select Your Event Plan *</label>
                                 <div class="select-wrapper">
                                     <select id="eventType" class="form-input select-input" required>
                                         <option value="" disabled selected>-- Select Your Event --</option>
                                         <option value="Engagement">Engagement Session</option>
                                         <option value="Wedding">Wedding Day</option>
                                         <option value="Homecoming">Homecoming Celebration</option>
                                         <option value="Wedding & Homecoming Combo">Wedding & Homecoming Combo</option>
                                         <option value="Pre-shoot, Wedding & Homecoming Combo">Pre-shoot, Wedding & Homecoming Combo</option>
                                     </select>
                                 </div>
                                 <small class="text-xs text-muted mt-2 block">Choose the primary event or package you're interested in.</small>
                             </div>
                             <div class="text-right mt-10"> <!-- Increased margin -->
                                 <button type="button" class="btn btn-primary next-step" data-current="1" data-next="2">Next: Details &rarr;</button>
                             </div>
                         </div>

                        <!-- Step 2: Event Details -->
                        <div class="form-section space-y-6" data-step="2">
                             <!-- Couple Names Section -->
                             <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="coupleNamesSection" style="display: none;">
                                  <div>
                                      <label for="brideName" class="form-label">Bride's Name *</label>
                                      <input type="text" id="brideName" class="form-input">
                                  </div>
                                  <div>
                                      <label for="groomName" class="form-label">Groom's Name *</label>
                                      <input type="text" id="groomName" class="form-input">
                                  </div>
                              </div>

                             <!-- Pre-Shoot Section (structure remains) -->
                            <div class="event-section space-y-6" id="preshoot-section" style="display:none;">
                                <h4 class="font-heading">Pre-Shoot Details (Triple Combo Package)</h4>
                                <div>
                                    <label for="preshootPackage" class="form-label">Triple Combo Package *</label>
                                     <div class="select-wrapper">
                                        <select id="preshootPackage" class="form-input select-input" required>
                                            <option value="" disabled selected>-- Select Triple Combo Package --</option>
                                            <option value="Pre-shoot, Wedding & Homecoming Combo Package 1">Combo Package 1 (150,000 LKR)</option>
                                            <option value="Pre-shoot, Wedding & Homecoming Combo Package 2">Combo Package 2 (200,000 LKR)</option>
                                            <option value="Pre-shoot, Wedding & Homecoming Combo Package 3">Combo Package 3 (270,000 LKR)</option>
                                        </select>
                                     </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                     <div>
                                         <label for="preshootDate" class="form-label">Preferred Pre-Shoot Date</label>
                                         <input type="date" id="preshootDate" class="form-input">
                                         <small class="text-xs text-muted mt-1 block">Approximate date is fine.</small>
                                     </div>
                                     <div>
                                        <label for="preshootLocation" class="form-label">Desired Location(s) / Vibe *</label>
                                        <input type="text" id="preshootLocation" class="form-input" placeholder="e.g., Beach, Hills, Urban, Garden">
                                    </div>
                                </div>
                                <div>
                                    <label for="outfitChanges" class="form-label">Number of Outfit Changes *</label>
                                    <input type="number" id="outfitChanges" class="form-input" min="1" value="2">
                                </div>
                            </div>

                            <!-- Engagement Section (structure remains) -->
                            <div class="event-section space-y-6" id="engagement-section" style="display:none;">
                                <h4 class="font-heading">Engagement Details</h4>
                                <div>
                                     <label for="engagementPackage" class="form-label">Engagement Package *</label>
                                     <div class="select-wrapper">
                                         <select id="engagementPackage" class="form-input select-input">
                                             <option value="" disabled selected>-- Select Package (See PDF) --</option>
                                             <option value="Engagement Package 1">Engagement Package 1 (35,000 LKR)</option>
                                             <option value="Engagement Package 2">Engagement Package 2 (55,000 LKR)</option>
                                             <option value="Engagement Package 3">Engagement Package 3 (75,000 LKR)</option>
                                         </select>
                                     </div>
                                 </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="engagementDate" class="form-label">Engagement Date *</label>
                                        <input type="date" id="engagementDate" class="form-input">
                                    </div>
                                     <div>
                                        <label for="engagementVenue" class="form-label">Venue / Hotel Name *</label>
                                        <input type="text" id="engagementVenue" class="form-input" placeholder="e.g., Grand Hotel Kandy">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="engagementLocation" class="form-label">Venue Location (City) *</label>
                                        <input type="text" id="engagementLocation" class="form-input" placeholder="e.g., Kandy">
                                    </div>
                                    <div>
                                        <label for="registrationTime" class="form-label">Registration Time *</label>
                                        <input type="time" id="registrationTime" class="form-input">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                     <div>
                                        <label for="ringTime" class="form-label">Ring Exchange Time (Approx.)</label>
                                        <input type="time" id="ringTime" class="form-input">
                                    </div>
                                    <div>
                                        <label for="engagementEndTime" class="form-label">Approx. Event End Time *</label>
                                        <input type="time" id="engagementEndTime" class="form-input">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="guestCount" class="form-label">Expected Guest Count *</label>
                                        <input type="number" id="guestCount" class="form-input" min="1" placeholder="Approximate number">
                                    </div>
                                     <div>
                                        <label for="brideMakeup" class="form-label">Makeup Artist / Salon (Optional)</label>
                                        <input type="text" id="brideMakeup" class="form-input">
                                    </div>
                                </div>
                                <div>
                                    <label for="specialRequests" class="form-label">Additional Requirements / Notes</label>
                                    <textarea id="specialRequests" class="form-input" rows="3" placeholder="Any specific shots, requests, or details?"></textarea>
                                </div>
                            </div>

                            <!-- Wedding Section (structure remains) -->
                             <div class="event-section space-y-6" id="wedding-section" style="display:none;">
                                <h4 class="font-heading">Wedding Details <span id="wedding-combo-note" style="display:none;">(Part of Triple Combo Package)</span></h4>
                                <div id="wedding-package-selector"> <!-- Wrap selector -->
                                     <label for="weddingPackage" class="form-label">Wedding Package *</label>
                                     <div class="select-wrapper">
                                         <select id="weddingPackage" class="form-input select-input">
                                             <option value="" disabled selected>-- Select Package (See PDF) --</option>
                                             <option value="Wedding Package 1">Wedding Package 1 (60,000 LKR)</option>
                                             <option value="Wedding Package 2">Wedding Package 2 (80,000 LKR)</option>
                                             <option value="Wedding Package 3">Wedding Package 3 (100,000 LKR)</option>
                                             <option value="Wedding Package 4">Wedding Package 4 (130,000 LKR)</option>
                                             <option value="Wedding Package 5">Wedding Package 5 (160,000 LKR)</option>
                                             <option value="Wedding Package 6">Wedding Package 6 (190,000 LKR)</option>
                                         </select>
                                     </div>
                                 </div>
                                 <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                     <div>
                                        <label for="weddingDate" class="form-label">Wedding Date *</label>
                                        <input type="date" id="weddingDate" class="form-input">
                                    </div>
                                     <div>
                                        <label for="weddingVenue" class="form-label">Venue / Hotel Name *</label>
                                        <input type="text" id="weddingVenue" class="form-input">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                     <div>
                                        <label for="weddingPhotoshootLocation" class="form-label">Primary Photoshoot Location *</label>
                                        <input type="text" id="weddingPhotoshootLocation" class="form-input" placeholder="E.g., Hotel grounds, Peradeniya Gardens">
                                    </div>
                                    <div>
                                        <label for="poruwaTime" class="form-label">Ceremony (Poruwa) Start Time *</label>
                                        <input type="time" id="poruwaTime" class="form-input">
                                    </div>
                                </div>
                                 <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                      <div>
                                        <label for="weddingStartTime" class="form-label">Desired Photography Start Time *</label>
                                        <input type="time" id="weddingStartTime" class="form-input" placeholder="e.g., Getting Ready shots">
                                    </div>
                                    <div>
                                        <label for="weddingEndTime" class="form-label">Approx. Event End Time *</label>
                                        <input type="time" id="weddingEndTime" class="form-input">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="weddingGuestCount" class="form-label">Expected Guest Count *</label>
                                        <input type="number" id="weddingGuestCount" class="form-input" min="1">
                                    </div>
                                    <div>
                                        <label for="weddingBrideMakeup" class="form-label">Bride's Makeup Artist / Salon (Optional)</label>
                                        <input type="text" id="weddingBrideMakeup" class="form-input">
                                    </div>
                                </div>
                                <div>
                                    <label for="weddingSpecialRequests" class="form-label">Additional Requirements / Notes</label>
                                    <textarea id="weddingSpecialRequests" class="form-input" rows="3" placeholder="Family photo list, special traditions, etc."></textarea>
                                </div>
                             </div>

                            <!-- Wedding & Homecoming Combo Package Selector (structure remains) -->
                            <div class="event-section" id="wedding-homecoming-section" style="display:none;">
                                 <h4 class="font-heading mb-4">Wedding & Homecoming Package</h4>
                                 <div>
                                     <label for="dualPackage" class="form-label">Select Combo Package *</label>
                                     <div class="select-wrapper">
                                         <select id="dualPackage" class="form-input select-input" required>
                                             <option value="" disabled selected>-- Select Combo Package (See PDF) --</option>
                                             <option value="Wedding & Homecoming Dual Package 1">Dual Package 1 (100,000 LKR)</option>
                                             <option value="Wedding & Homecoming Dual Package 2">Dual Package 2 (160,000 LKR)</option>
                                             <option value="Wedding & Homecoming Dual Package 3">Dual Package 3 (200,000 LKR)</option>
                                         </select>
                                     </div>
                                      <small class="text-xs text-muted mt-1 block">Details for both events will be collected below.</small>
                                 </div>
                             </div>

                            <!-- Homecoming Section (structure remains) -->
                             <div class="event-section space-y-6" id="homecoming-section" style="display:none;">
                                 <h4 class="font-heading">Homecoming Details <span id="homecoming-combo-note" style="display:none;">(Part of Triple Combo Package)</span></h4>
                                 <!-- Package selector only shown if Homecoming is selected ALONE -->
                                 <div id="homecoming-package-selector" style="display: none;">
                                      <label for="homecomingPackage" class="form-label">Homecoming Package *</label>
                                      <div class="select-wrapper">
                                          <select id="homecomingPackage" class="form-input select-input">
                                              <option value="" disabled selected>-- Select Package (See PDF) --</option>
                                              <option value="Homecoming Package 1">Homecoming Package 1 (30,000 LKR)</option>
                                              <option value="Homecoming Package 2">Homecoming Package 2 (50,000 LKR)</option>
                                              <option value="Homecoming Package 3">Homecoming Package 3 (70,000 LKR)</option>
                                              <option value="Homecoming Package 4">Homecoming Package 4 (90,000 LKR)</option>
                                              <option value="Homecoming Package 5">Homecoming Package 5 (150,000 LKR)</option>
                                              <option value="Homecoming Package 6">Homecoming Package 6 (200,000 LKR)</option>
                                          </select>
                                      </div>
                                  </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="homecomingDate" class="form-label">Homecoming Date *</label>
                                        <input type="date" id="homecomingDate" class="form-input">
                                    </div>
                                     <div>
                                        <label for="homecomingVenue" class="form-label">Venue / Hotel Name *</label>
                                        <input type="text" id="homecomingVenue" class="form-input">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                      <div>
                                        <label for="homecomingLocation" class="form-label">Primary Photoshoot Location *</label>
                                        <input type="text" id="homecomingLocation" class="form-input" placeholder="E.g., Venue gardens, home">
                                    </div>
                                     <div>
                                        <label for="homecomingStartTime" class="form-label">Desired Photography Start Time *</label>
                                        <input type="time" id="homecomingStartTime" class="form-input">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                     <div>
                                        <label for="homecomingEndTime" class="form-label">Approx. Event End Time *</label>
                                        <input type="time" id="homecomingEndTime" class="form-input">
                                    </div>
                                    <div>
                                        <label for="homecomingGuestCount" class="form-label">Expected Guest Count *</label>
                                        <input type="number" id="homecomingGuestCount" class="form-input" min="1">
                                    </div>
                                </div>
                                 <div>
                                    <label for="homecomingBrideMakeup" class="form-label">Bride's Makeup Artist / Salon (Optional)</label>
                                    <input type="text" id="homecomingBrideMakeup" class="form-input">
                                </div>
                                <div>
                                    <label for="homecomingSpecialRequests" class="form-label">Additional Requirements / Notes</label>
                                    <textarea id="homecomingSpecialRequests" class="form-input" rows="3"></textarea>
                                </div>
                             </div>

                            <!-- Step 2 Navigation -->
                            <div class="flex justify-between items-center mt-10 border-t border-light-custom pt-6">
                                <button type="button" class="btn btn-outline prev-step" data-current="2" data-prev="1">&larr; Back: Event</button>
                                <button type="button" class="btn btn-primary next-step" data-current="2" data-next="3">Next: Contact &rarr;</button>
                            </div>
                        </div>

                        <!-- Step 3: Contact Info -->
                        <div class="form-section space-y-6" data-step="3">
                             <h4 class="font-heading mb-6">Your Contact Information</h4>
                             <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                 <div>
                                     <label for="phoneNumber" class="form-label">Primary Phone Number *</label>
                                     <input type="tel" id="phoneNumber" class="form-input" required placeholder="e.g., +94 77 123 4567">
                                 </div>
                                 <div>
                                     <label for="emailAddress" class="form-label">Email Address *</label>
                                     <input type="email" id="emailAddress" class="form-input" required placeholder="<EMAIL>">
                                 </div>
                             </div>
                             <div>
                                 <label for="additionalContact" class="form-label">How did you hear about me? / Additional Notes</label>
                                 <textarea id="additionalContact" class="form-input" rows="4" placeholder="e.g., Facebook, Instagram, Friend Recommendation, Other questions..."></textarea>
                             </div>
                              <!-- Step 3 Navigation -->
                             <div class="flex justify-between items-center mt-10 border-t border-light-custom pt-6">
                                 <button type="button" class="btn btn-outline prev-step" data-current="3" data-prev="2">&larr; Back: Details</button>
                                 <button type="button" class="btn btn-primary next-step" data-current="3" data-next="4">Review Booking &rarr;</button>
                             </div>
                         </div>

                        <!-- Step 4: Review & Confirmation -->
                        <div class="form-section space-y-6" data-step="4">
                             <h4 class="font-heading mb-6">Review Your Booking Inquiry</h4>
                              <div id="bookingSummary" class="p-6 bg-light rounded-md space-y-3 text-sm border border-light-custom mb-6">
                                 <!-- Summary content will be dynamically generated here -->
                                 <p class="text-center text-muted">Loading summary...</p>
                             </div>
                             <div class="mt-6">
                                 <label class="flex items-center space-x-3 cursor-pointer">
                                     <input type="checkbox" id="termsCheckbox" class="form-checkbox h-5 w-5 text-secondary rounded border-gray-300 focus:ring-secondary focus:ring-offset-0 cursor-pointer" required>
                                     <span class="text-sm">I confirm the details above are accurate and understand this is an inquiry. I agree to the <a href="#" class="text-secondary hover:underline font-medium" target="_blank" title="View terms - opens new tab">booking terms & conditions</a>.*</span>
                                 </label>
                                 <small class="text-xs text-muted mt-2 block">Submitting this form sends an inquiry. Booking is confirmed upon contract signing and deposit payment.</small>
                             </div>
                             <!-- Step 4 Navigation -->
                             <div class="flex flex-col sm:flex-row justify-between items-center mt-10 border-t border-light-custom pt-6 gap-4">
                                 <button type="button" class="btn btn-outline prev-step w-full sm:w-auto" data-current="4" data-prev="3">&larr; Edit Details</button>
                                 <button type="submit" class="btn btn-primary w-full sm:w-auto">Submit Inquiry &rarr;</button>
                             </div>
                         </div>

                         <div id="form-message" class="mt-6 text-center font-medium"></div>
                         <div id="loading-indicator" class="mt-6 text-center hidden">
                             <i class="fas fa-spinner fa-spin text-secondary text-2xl"></i>
                             <p class="text-secondary text-sm mt-2">Submitting booking...</p>
                         </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Invoice Section (Remains Hidden, styled by CSS) -->
    <section id="invoice" class="section-padding bg-light-alt" style="display:none;"> <!-- Changed background -->
        <div class="container-xl">
             <div class="text-center mb-12">
                <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i> <!-- Success Icon -->
                <h2 class="font-heading mb-4">Inquiry Sent Successfully!</h2>
                <p class="text-lg text-muted max-w-3xl mx-auto">Thank you for your interest! I've received your inquiry and will be in touch via email within 24-48 hours to discuss details and confirm availability. Below is a summary of your request (this is not a final invoice).</p>
            </div>
            <div class="max-w-4xl mx-auto">
                <!-- Invoice structure remains, styled by CSS -->
                 <div class="invoice">
                     <div class="flex flex-col md:flex-row justify-between items-start mb-8 pb-6 border-b border-light-custom">
                         <div class="invoice-logo mb-4 md:mb-0">
                             Chalaka Dulanga Photography
                             <p class="text-xs text-muted font-body uppercase tracking-wider mt-1">Booking Inquiry Summary</p>
                         </div>
                         <div class="text-sm text-right">
                             <p class="mb-1"><strong>Reference #:</strong> <span id="invoiceNumber"></span></p>
                             <p class="mb-0"><strong>Date Submitted:</strong> <span id="invoiceDate"></span></p>
                         </div>
                     </div>

                     <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                         <div>
                             <h5 class="font-semibold mb-2">Inquiry For:</h5>
                             <p class="mb-1" id="invoiceClientName"></p>
                             <p class="mb-1" id="invoiceClientEmail"></p>
                             <p class="mb-0" id="invoiceClientPhone"></p>
                         </div>
                         <div class="md:text-right">
                              <h5 class="font-semibold mb-2">From:</h5>
                              <p class="mb-1">Chalaka Dulanga Photography</p>
                              <p class="mb-1">Kandy, Sri Lanka</p>
                              {/*<p class="mb-1">68/1 Sri Amarawansha Mawatha</p>
                              <p class="mb-1">Kandy 20400, Sri Lanka</p> */}
                              <p class="mb-0">+94 76 324 9526</p>
                         </div>
                     </div>

                     <h5 class="font-semibold mb-3">Service Request Details:</h5>
                     <div class="overflow-x-auto mb-8">
                         <table class="min-w-full invoice-table">
                             <thead>
                                 <tr>
                                     <th class="w-2/5">Description</th>
                                     <th>Event Date(s)</th>
                                     <th>Package / Type</th>
                                     <th class="text-right">Estimated Price (LKR)</th>
                                 </tr>
                             </thead>
                             <tbody id="invoiceItems">
                                 <!-- Items will be added dynamically -->
                             </tbody>
                             <tfoot>
                                 <tr class="bg-light">
                                     <td colspan="3" class="text-right font-semibold"><strong>Estimated Total:</strong></td>
                                     <td id="invoiceTotal" class="text-right font-semibold"></td>
                                 </tr>
                             </tfoot>
                         </table>
                     </div>

                      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                         <div>
                             <h5 class="font-semibold mb-2">Next Steps:</h5>
                             <p class="text-sm mb-1">1. I will check availability for your date(s).</p>
                             <p class="text-sm mb-1">2. I will email you to confirm details or ask further questions.</p>
                             <p class="text-sm mb-1">3. If available, a formal quote & contract will be sent.</p>
                             <p class="text-sm mb-0">4. Booking is secured upon signed contract & deposit.</p>
                         </div>
                         <div>
                             <h5 class="font-semibold mb-2">Important Notes:</h5>
                             <p class="text-sm mb-1">The price shown is an estimate based on standard packages and subject to confirmation.</p>
                              <p class="text-sm mb-1">Travel fees may apply for locations outside Kandy central.</p>
                             <p class="text-sm mb-1">Please check your spam folder if you don't hear back soon!</p>
                         </div>
                     </div>

                     <div class="invoice-footer">
                         <p>Thank you again for considering Chalaka Dulanga Photography!</p>
                     </div>
                 </div>
                 <div class="text-center mt-8 flex flex-col sm:flex-row justify-center gap-4">
                     <button id="downloadInvoice" class="btn btn-outline-secondary"><i class="fas fa-download mr-2"></i>Download PDF</button>
                     <button id="returnToHome" class="btn btn-primary">Return to Homepage</button>
                 </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding bg-light"> <!-- Changed background -->
        <div class="container-xl">
            <div class="text-center mb-16">
                <h2 class="font-heading mb-4">Get In Touch</h2>
                <p class="text-lg text-muted max-w-3xl mx-auto">Have questions, need more information, or just want to say hello? Reach out using the details below or send a message directly.</p>
            </div>
             <!-- Structure remains, styled by CSS -->
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-12 lg:gap-16">
                 <!-- Contact Info -->
                 <div class="lg:col-span-2 space-y-6">
                      <h4 class="font-heading">Contact Information</h4>
                      <div class="flex items-start space-x-4">
                         <i class="fas fa-map-marker-alt fa-lg mt-1"></i>
                         <div>
                             <h5 class="font-semibold mb-1">Location</h5>
                             <p class="text-muted">Kandy, Sri Lanka <br>(Studio visits by appointment)</p>
                         </div>
                     </div>
                      <div class="flex items-start space-x-4">
                         <i class="fas fa-phone-alt fa-lg mt-1"></i>
                         <div>
                             <h5 class="font-semibold mb-1">Phone / WhatsApp</h5>
                             <p class="text-muted"><a href="tel:+94763249526" class="hover:text-secondary">+94 76 324 9526</a></p>
                         </div>
                     </div>
                      <div class="flex items-start space-x-4">
                         <i class="fas fa-envelope fa-lg mt-1"></i>
                         <div>
                             <h5 class="font-semibold mb-1">Email</h5>
                             <p class="text-muted"><a href="mailto:<EMAIL>" class="hover:text-secondary break-all"><EMAIL></a></p>
                         </div>
                     </div>
                     <div class="flex items-start space-x-4">
                         <i class="fas fa-clock fa-lg mt-1"></i>
                         <div>
                             <h5 class="font-semibold mb-1">Business Hours</h5>
                             <p class="text-muted text-sm">Mon - Sat: 9:00 AM - 6:00 PM<br>Event bookings accepted for any day.</p>
                         </div>
                     </div>
                      <div class="pt-4">
                          <h5 class="font-semibold mb-3">Connect Online</h5>
                          <div class="flex space-x-4">
                              <a href="https://www.facebook.com/chalakadulangaphotography/" target="_blank" rel="noopener noreferrer" title="Facebook">
                                  <i class="fab fa-facebook-f fa-lg" aria-hidden="true"></i>
                                  <span class="sr-only">Facebook</span>
                              </a>
                              <a href="https://www.instagram.com/chalaka_dulanga_photography_/" target="_blank" rel="noopener noreferrer" title="Instagram">
                                  <i class="fab fa-instagram fa-lg" aria-hidden="true"></i>
                                  <span class="sr-only">Instagram</span>
                              </a>
                              <a href="https://www.tiktok.com/@chalakadulanga98" target="_blank" rel="noopener noreferrer" title="TikTok">
                                  <i class="fab fa-tiktok fa-lg" aria-hidden="true"></i>
                                  <span class="sr-only">TikTok</span>
                              </a>
                              <a href="https://wa.me/94763249526" target="_blank" rel="noopener noreferrer" title="WhatsApp">
                                  <i class="fab fa-whatsapp fa-lg" aria-hidden="true"></i>
                                  <span class="sr-only">WhatsApp</span>
                              </a>
                          </div>
                      </div>
                 </div>
                 <!-- Contact Form -->
                 <div class="lg:col-span-3">
                      <h4 class="font-heading">Send a Direct Message</h4>
                      <!-- Form structure remains, styled by CSS -->
                      <form id="contactForm" class="space-y-5">
                         <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
                             <div>
                                 <label for="contactName" class="form-label">Your Name *</label>
                                 <input type="text" id="contactName" name="name" class="form-input" required>
                             </div>
                             <div>
                                 <label for="contactEmail" class="form-label">Your Email *</label>
                                 <input type="email" id="contactEmail" name="email" class="form-input" required>
                             </div>
                         </div>
                         <div>
                             <label for="contactSubject" class="form-label">Subject *</label>
                             <input type="text" id="contactSubject" name="subject" class="form-input" required placeholder="e.g., Question about packages, Availability check">
                         </div>
                         <div>
                             <label for="contactMessage" class="form-label">Your Message *</label>
                             <textarea id="contactMessage" name="message" class="form-input" rows="6" required></textarea>
                         </div>
                         <div>
                             <button type="submit" class="btn btn-primary">Send Message</button>
                         </div>
                     </form>
                 </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light pt-20 pb-10"> <!-- Adjusted padding -->
        <div class="container-xl">
            <!-- Structure remains, styled by CSS -->
             <div class="grid grid-cols-1 md:grid-cols-12 gap-8 mb-12">
                 <!-- Column 1: Brand -->
                 <div class="md:col-span-5 lg:col-span-4">
                     <h5 class="font-heading text-xl mb-4 text-white">Chalaka Dulanga</h5>
                     <p class="text-sm opacity-70 mb-6 pr-4">Elegant wedding and event photography based in Kandy, Sri Lanka. Capturing authentic moments with an artistic touch to create timeless memories.</p>
                     <div class="flex space-x-4">
                          <a href="https://www.facebook.com/chalakadulangaphotography/" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-facebook-f" aria-hidden="true"></i><span class="sr-only">Facebook</span></a>
                          <a href="https://www.instagram.com/chalaka_dulanga_photography_/" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-instagram" aria-hidden="true"></i><span class="sr-only">Instagram</span></a>
                          <a href="https://www.tiktok.com/@chalakadulanga98" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-tiktok" aria-hidden="true"></i><span class="sr-only">TikTok</span></a>
                          <a href="https://wa.me/94763249526" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-whatsapp" aria-hidden="true"></i><span class="sr-only">WhatsApp</span></a>
                     </div>
                 </div>
                  <!-- Column 2: Quick Links -->
                  <div class="md:col-span-3 lg:col-span-2">
                      <h5 class="text-base mb-4 text-white">Explore</h5>
                      <ul class="space-y-2 text-sm">
                         <li><a href="#home" class="opacity-70 hover:opacity-100">Home</a></li>
                         <li><a href="#about" class="opacity-70 hover:opacity-100">About</a></li>
                         <li><a href="#services" class="opacity-70 hover:opacity-100">Services</a></li>
                         <li><a href="#portfolio" class="opacity-70 hover:opacity-100">Portfolio</a></li>
                         <li><a href="#booking" class="opacity-70 hover:opacity-100">Book Now</a></li>
                         <li><a href="#contact" class="opacity-70 hover:opacity-100">Contact</a></li>
                         <li><a href="Chalaka-Dulanga-Photography-Packages.pdf" target="_blank" class="opacity-70 hover:opacity-100">Packages (PDF)</a></li>
                     </ul>
                  </div>
                  <!-- Column 3: Contact -->
                  <div class="md:col-span-4 lg:col-span-3">
                       <h5 class="text-base mb-4 text-white">Contact Info</h5>
                       <ul class="space-y-3 text-sm opacity-70">
                           <li class="flex items-start">
                               <i class="fas fa-map-marker-alt mt-1 flex-shrink-0"></i>
                               <span>Based in Kandy,<br>Sri Lanka</span>
                           </li>
                           <li class="flex items-center">
                              <i class="fas fa-phone-alt flex-shrink-0"></i>
                              <a href="tel:+94763249526" class="hover:opacity-100 hover:text-secondary">+94 76 324 9526</a>
                           </li>
                           <li class="flex items-center">
                              <i class="fas fa-envelope flex-shrink-0"></i>
                              <a href="mailto:<EMAIL>" class="hover:opacity-100 hover:text-secondary break-all"><EMAIL></a>
                           </li>
                      </ul>
                   </div>
                    <!-- Column 4: Social Media Links -->
                    <div class="md:col-span-12 lg:col-span-3">
                         <h5 class="text-base mb-4 text-white">Follow Us</h5>
                         <div class="flex space-x-4">
                             <a href="https://www.facebook.com/chalakadulangaphotography/" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-facebook-f fa-lg" aria-hidden="true"></i><span class="sr-only">Facebook</span></a>
                             <a href="https://www.instagram.com/chalaka_dulanga_photography_/" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-instagram fa-lg" aria-hidden="true"></i><span class="sr-only">Instagram</span></a>
                             <a href="https://wa.me/94763249526" target="_blank" rel="noopener noreferrer" class="opacity-70 hover:opacity-100 hover:text-secondary transition duration-300"><i class="fab fa-whatsapp fa-lg" aria-hidden="true"></i><span class="sr-only">WhatsApp</span></a>
                         </div>
                    </div>
             </div>
             <hr class="border-t border-dark-custom my-8">
             <div class="text-center text-sm opacity-60">
                 <p>&copy; <span id="currentYear"></span> Chalaka Dulanga Photography. All Rights Reserved.</p>
                 <p>Crafting visual stories with passion.</p>
             </div>
         </div>
     </footer>

    <script>
        // --- Standard JavaScript from original, no functional changes needed for this redesign ---
        document.addEventListener('DOMContentLoaded', function() {
            // Set Current Year in Footer
            document.getElementById('currentYear').textContent = new Date().getFullYear();

            // --- Hero Slideshow ---
            let currentSlide = 0;
            let slideshowInterval;
            const slides = document.querySelectorAll('.slide');
            const indicators = document.querySelectorAll('.indicator');
            const totalSlides = slides.length;

            function changeSlide(direction) {
                if (!slides.length) return; // Guard against no slides
                currentSlide = (currentSlide + direction + totalSlides) % totalSlides;
                updateSlideshow();
            }

            function setSlide(index) {
                if (!slides.length) return;
                currentSlide = index;
                updateSlideshow();
                resetSlideshow();
            }

            function updateSlideshow() {
                slides.forEach((slide, index) => {
                     slide.classList.toggle('active', index === currentSlide);
                 });
                 indicators.forEach((indicator, index) => {
                    // Use index directly, assuming indicators match slides order
                    indicator.classList.toggle('active', index === currentSlide);
                 });
            }


            function startSlideshow() {
                if (!slides.length) return;
                 // Clear existing interval before starting a new one
                clearInterval(slideshowInterval);
                slideshowInterval = setInterval(() => {
                    changeSlide(1);
                }, 5000); // Change slide every 5 seconds
            }

            function resetSlideshow() {
                if (!slides.length) return;
                clearInterval(slideshowInterval);
                startSlideshow();
            }

            // Initialize slideshow
            if (slides.length > 0) {
                 updateSlideshow();
                 startSlideshow();

                 // Attach listeners for buttons and indicators
                 const prevButton = document.querySelector('.slideshow-nav.prev');
                 const nextButton = document.querySelector('.slideshow-nav.next');

                 if (prevButton && nextButton) {
                     prevButton.addEventListener('click', () => {
                         changeSlide(-1);
                         resetSlideshow();
                     });
                     nextButton.addEventListener('click', () => {
                         changeSlide(1);
                         resetSlideshow();
                     });
                 }

                // Use direct index for indicators
                indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => {
                         setSlide(index); // Use the index directly
                    });
                });

            } else {
                 console.log("No slides found for slideshow.");
            }


            // --- Navigation ---
            const navbar = document.getElementById('mainNav');
            const navbarToggler = document.getElementById('navbarToggler');
            const mobileMenu = document.getElementById('mobileMenu');
            const navLinks = mobileMenu.querySelectorAll('.nav-link');
            const desktopNavLinks = document.querySelectorAll('#navbarNav .nav-link'); // Desktop links for active state

             // Function to update active nav link
            function updateActiveNavLink() {
                let currentSection = '';
                const sections = document.querySelectorAll('section[id]'); // Only sections with IDs

                sections.forEach(section => {
                     const sectionTop = section.offsetTop;
                     // Adjust top offset based on navbar height + some buffer
                     if (pageYOffset >= sectionTop - navbar.offsetHeight - 50) {
                         currentSection = section.getAttribute('id');
                     }
                 });

                 // Update both mobile and desktop links
                 const allLinks = [...navLinks, ...desktopNavLinks];
                 allLinks.forEach(link => {
                     link.classList.remove('active');
                     if (link.getAttribute('href') === `#${currentSection}`) {
                         link.classList.add('active');
                     }
                 });
             }

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
                updateActiveNavLink(); // Update active link on scroll
            });

            // Initial active link check
            updateActiveNavLink();

            // Mobile menu toggle
            if(navbarToggler && mobileMenu) {
                 navbarToggler.addEventListener('click', function() {
                     const isShown = mobileMenu.classList.toggle('show');
                     document.body.style.overflow = isShown ? 'hidden' : ''; // Toggle body scroll lock

                     // Toggle icon
                     const icon = navbarToggler.querySelector('i');
                     if (isShown) {
                         icon.classList.remove('fa-bars');
                         icon.classList.add('fa-times');
                     } else {
                         icon.classList.remove('fa-times');
                         icon.classList.add('fa-bars');
                     }
                 });
            }

            // Close mobile menu when a link is clicked
             navLinks.forEach(link => {
                 link.addEventListener('click', () => {
                     if (mobileMenu.classList.contains('show')) {
                          mobileMenu.classList.remove('show');
                          document.body.style.overflow = ''; // Ensure scroll is re-enabled
                          const icon = navbarToggler.querySelector('i');
                          icon.classList.remove('fa-times');
                          icon.classList.add('fa-bars');
                     }
                 });
             });


            // --- Fade-in Animation ---
            const fadeElements = document.querySelectorAll('.fade-in');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('appear');
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.15 }); // Adjust threshold slightly

            fadeElements.forEach(element => {
                observer.observe(element);
            });

            // --- Booking Form Logic ---
            const bookingForm = document.getElementById('bookingForm');
            if (bookingForm) { // Check if form exists
                const formSections = bookingForm.querySelectorAll('.form-section');
                const steps = document.querySelectorAll('.step');
                const nextButtons = bookingForm.querySelectorAll('.next-step');
                const prevButtons = bookingForm.querySelectorAll('.prev-step');
                const eventTypeSelect = document.getElementById('eventType');

                 function updateStepIndicator(targetStep) {
                     steps.forEach((step) => {
                         const stepNumber = parseInt(step.dataset.step);
                         step.classList.toggle('active', stepNumber <= targetStep);
                     });
                 }

                 function showFormSection(targetStep) {
                     formSections.forEach(section => {
                         section.classList.toggle('active', parseInt(section.dataset.step) === targetStep);
                     });
                     updateStepIndicator(targetStep);

                     // Scroll to top of form container smoothly
                     const formContainer = document.querySelector('.booking-form-container');
                     if (formContainer) {
                        // Slightly delay scroll to allow section to render if needed
                        setTimeout(() => {
                            formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }, 50);
                     }
                  }

                  // --- Form Validation ---
                  function validateStep(stepNumber) {
                     const currentSection = bookingForm.querySelector(`.form-section[data-step="${stepNumber}"]`);
                     if (!currentSection) return true; // Should not happen

                     const requiredInputs = currentSection.querySelectorAll('[required]');
                     let isValid = true;

                     requiredInputs.forEach(input => {
                         // Reset border first
                         input.style.borderColor = ''; // Reset to default CSS

                         if (!input.value || (input.type === 'checkbox' && !input.checked)) {
                             isValid = false;
                             input.style.borderColor = 'red'; // Highlight invalid field
                              // Focus the first invalid field
                             if (isValid === false && !bookingForm.querySelector(':invalid')) {
                                 input.focus();
                             }
                         } else if (input.type === 'email' && !/^\S+@\S+\.\S+$/.test(input.value)) {
                             // Basic email format check
                             isValid = false;
                             input.style.borderColor = 'red';
                         } else {
                             input.style.borderColor = ''; // Valid, ensure border is default
                         }
                     });

                     // Custom validation for specific fields if needed
                      if (stepNumber === 1 && !eventTypeSelect.value) {
                         isValid = false;
                         eventTypeSelect.style.borderColor = 'red';
                         if(!bookingForm.querySelector(':invalid')) eventTypeSelect.focus();
                      } else if (eventTypeSelect.style.borderColor === 'red') {
                         eventTypeSelect.style.borderColor = ''; // Reset if now valid
                      }

                      // Special validation for triple combo package
                      if (stepNumber === 2 && eventTypeSelect.value === 'Pre-shoot, Wedding & Homecoming Combo') {
                          const preshootPackageSelect = document.getElementById('preshootPackage');
                          if (preshootPackageSelect && !preshootPackageSelect.value) {
                              isValid = false;
                              preshootPackageSelect.style.borderColor = 'red';
                              console.log('Triple combo validation failed: preshootPackage is required');
                              if(!bookingForm.querySelector(':invalid')) preshootPackageSelect.focus();
                          }
                      }


                     if (!isValid) {
                         // Find the first invalid input to report it
                         let firstInvalidInput = null;
                         requiredInputs.forEach(input => {
                             if (!firstInvalidInput && (!input.value || (input.type === 'checkbox' && !input.checked) || (input.type === 'email' && !/^\S+@\S+\.\S+$/.test(input.value)))) {
                                 firstInvalidInput = input;
                             }
                         });
                         const fieldName = firstInvalidInput?.id || firstInvalidInput?.name || 'Unknown Field';
                         const label = bookingForm.querySelector(`label[for="${fieldName}"]`);
                         const labelText = label ? label.textContent.replace('*','').trim() : fieldName;
                         alert(`Please fill in the required field: "${labelText}"`);
                     }
                     return isValid;
                  }


                 nextButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const currentStep = parseInt(button.dataset.current);
                        const nextStep = parseInt(button.dataset.next);

                        if (!validateStep(currentStep)) {
                            return; // Stop if validation fails
                        }

                         // Validation before generating summary (Step 3 to 4)
                         if(currentStep === 3) {
                             // Validation handled by validateStep
                             generateSummary(); // Generate summary before showing step 4
                         }

                        showFormSection(nextStep);
                    });
                 });

                  prevButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const prevStep = parseInt(button.dataset.prev);
                        showFormSection(prevStep);
                    });
                 });

                 // Handle dynamic form sections based on event type
                 eventTypeSelect.addEventListener('change', function() {
                     const selectedEvent = this.value;
                     console.log('Selected Event:', selectedEvent);

                     const allEventSections = document.querySelectorAll('.event-section');
                     const coupleNamesSection = document.getElementById('coupleNamesSection');
                     const weddingPackageSelector = document.getElementById('wedding-package-selector'); // Specific div
                     const homecomingPackageSelector = document.getElementById('homecoming-package-selector');
                     const preshootPackageSelector = document.getElementById('preshoot-section').querySelector('.select-wrapper'); // Assuming first select is package
                     const weddingHomecomingPackageSelector = document.getElementById('wedding-homecoming-section');

                     console.log('Wedding & Homecoming Section:', weddingHomecomingPackageSelector);

                     // Hide all optional event sections and specific selectors first
                     allEventSections.forEach(sec => sec.style.display = 'none');
                     coupleNamesSection.style.display = 'none';
                     if (weddingPackageSelector) weddingPackageSelector.style.display = 'none';
                     if (homecomingPackageSelector) homecomingPackageSelector.style.display = 'none';
                     if (preshootPackageSelector) preshootPackageSelector.closest('.event-section').style.display = 'none'; // Hide entire pre-shoot section
                     if (weddingHomecomingPackageSelector) weddingHomecomingPackageSelector.style.display = 'none';


                     // Function to set required attribute based on visibility
                    function toggleRequired(sectionId, isRequired) {
                         console.log(`Toggling required for section ${sectionId}: ${isRequired}`);
                         const section = document.getElementById(sectionId);
                         if (section) {
                             // Special handling for wedding-homecoming-section
                             if (sectionId === 'wedding-homecoming-section') {
                                 const dualPackageSelect = document.getElementById('dualPackage');
                                 if (dualPackageSelect) {
                                     dualPackageSelect.required = isRequired;
                                     console.log(`Set dualPackage required to ${isRequired}`);
                                 }
                             }

                             // Special handling for preshoot-section
                             if (sectionId === 'preshoot-section') {
                                 const preshootPackageSelect = document.getElementById('preshootPackage');
                                 if (preshootPackageSelect) {
                                     preshootPackageSelect.required = isRequired;
                                     console.log(`Set preshootPackage required to ${isRequired}`);
                                 }
                             }

                             const inputs = section.querySelectorAll('input, select, textarea');
                             console.log(`Found ${inputs.length} inputs in section ${sectionId}`);
                             inputs.forEach(input => {
                                 // Only toggle 'required' for elements that CAN be required
                                 // Avoid setting required on optional fields like makeup artist
                                 // Check if the label associated with input has '*'
                                 const label = bookingForm.querySelector(`label[for="${input.id}"]`);
                                 const isComboEvent = ['Wedding & Homecoming Combo', 'Pre-shoot, Wedding & Homecoming Combo'].includes(eventTypeSelect.value);
                                 const isIndividualPackageSelector = ['weddingPackage', 'homecomingPackage'].includes(input.id);

                                 // --- Start of Added Condition ---
                                 // Skip setting required for individual package selectors if a combo is selected
                                 // Their requirement is handled by the specific combo logic earlier.
                                 if (isComboEvent && isIndividualPackageSelector) {
                                     console.log(`Skipping general required toggle for ${input.id} because a combo event is selected.`);
                                 }
                                 // --- End of Added Condition ---
                                 else if (label && label.textContent.includes('*')) {
                                     input.required = isRequired;
                                     console.log(`Setting ${input.id} required to ${isRequired} (has * in label)`);
                                 } else if (!label && input.id.toLowerCase().includes('package') && input.tagName === 'SELECT') {
                                      // Special case for package selectors if labels dont have * but are essential
                                      input.required = isRequired;
                                      console.log(`Setting ${input.id} required to ${isRequired} (package selector)`);
                                 } else if (['brideName', 'groomName', 'phoneNumber', 'emailAddress', 'termsCheckbox'].includes(input.id)) {
                                      // Always required fields (handled elsewhere for terms)
                                      input.required = true; // Ensure these are always required if their section is visible
                                      console.log(`Setting ${input.id} required to true (always required)`);
                                 } else {
                                      // For other fields, explicitly set required to false if section is hidden
                                      if (isRequired === false && input.hasAttribute('required')) {
                                         input.required = false;
                                         console.log(`Setting ${input.id} required to false (section hidden)`);
                                      }
                                 }
                             });
                         }
                    }

                     // Show relevant sections and manage required fields
                    const sectionsToShow = [];
                    if (selectedEvent) {
                         coupleNamesSection.style.display = 'grid';
                         coupleNamesSection.querySelectorAll('input').forEach(inp => inp.required = true); // Couple names always required if section shown

                         if (selectedEvent === 'Engagement') {
                             sectionsToShow.push('engagement-section');
                             if (weddingPackageSelector) weddingPackageSelector.style.display = 'none'; // Ensure other pkg selectors hidden
                         } else if (selectedEvent === 'Wedding') {
                             sectionsToShow.push('wedding-section');
                             if (weddingPackageSelector) weddingPackageSelector.style.display = 'block'; // Show specific wedding selector
                         } else if (selectedEvent === 'Homecoming') {
                             sectionsToShow.push('homecoming-section');
                             if (homecomingPackageSelector) homecomingPackageSelector.style.display = 'block'; // Show specific HC selector
                         } else if (selectedEvent === 'Wedding & Homecoming Combo') {
                             console.log('Showing Wedding & Homecoming Combo sections');
                             sectionsToShow.push('wedding-section', 'homecoming-section', 'wedding-homecoming-section');

                             if (weddingHomecomingPackageSelector) {
                                 console.log('Setting wedding-homecoming-section display to block');
                                 weddingHomecomingPackageSelector.style.display = 'block'; // Show combo selector
                             } else {
                                 console.log('wedding-homecoming-section not found!');
                             }

                             if (weddingPackageSelector) weddingPackageSelector.style.display = 'none'; // Hide individual wedding selector
                             if (homecomingPackageSelector) homecomingPackageSelector.style.display = 'none'; // Hide individual HC selector

                         } else if (selectedEvent === 'Pre-shoot, Wedding & Homecoming Combo') {
                             console.log('Showing Pre-shoot, Wedding & Homecoming Combo sections');
                             sectionsToShow.push('preshoot-section', 'wedding-section', 'homecoming-section');
                             // Removed wedding-homecoming-section from sectionsToShow for triple combo

                             if (preshootPackageSelector) {
                                 preshootPackageSelector.closest('.event-section').style.display = 'block'; // Show preshoot section
                                 // Explicitly set preshootPackage as required
                                 const preshootPackageSelect = document.getElementById('preshootPackage');
                                 if (preshootPackageSelect) {
                                     preshootPackageSelect.required = true;
                                     console.log('Explicitly setting preshootPackage required to true for triple combo');
                                 }
                             }
                             // Explicitly hide individual package selectors and mark them as not required for triple combo
                             if (weddingPackageSelector) {
                                 weddingPackageSelector.style.display = 'none';
                                 const weddingPackageSelect = document.getElementById('weddingPackage');
                                 if (weddingPackageSelect) {
                                     weddingPackageSelect.required = false;
                                     console.log('Explicitly setting weddingPackage required to false for triple combo');
                                 }
                             }
                             if (homecomingPackageSelector) {
                                 homecomingPackageSelector.style.display = 'none';
                                 const homecomingPackageSelect = document.getElementById('homecomingPackage');
                                 if (homecomingPackageSelect) {
                                     homecomingPackageSelect.required = false;
                                     console.log('Explicitly setting homecomingPackage required to false for triple combo');
                                 }
                             }

                             // Hide the wedding-homecoming-section for triple combo
                             if (weddingHomecomingPackageSelector) {
                                 console.log('Hiding wedding-homecoming-section for triple combo');
                                 weddingHomecomingPackageSelector.style.display = 'none';
                             }

                             // Show the combo notes
                             const weddingComboNote = document.getElementById('wedding-combo-note');
                             const homecomingComboNote = document.getElementById('homecoming-combo-note');
                             if (weddingComboNote) weddingComboNote.style.display = 'inline';
                             if (homecomingComboNote) homecomingComboNote.style.display = 'inline';
                         }
                    }

                    // Hide combo notes by default
                    const weddingComboNote = document.getElementById('wedding-combo-note');
                    const homecomingComboNote = document.getElementById('homecoming-combo-note');
                    if (weddingComboNote) weddingComboNote.style.display = 'none';
                    if (homecomingComboNote) homecomingComboNote.style.display = 'none';

                    // Show selected sections and hide others, toggle required
                    allEventSections.forEach(sec => {
                        const id = sec.getAttribute('id');
                        const shouldShow = sectionsToShow.includes(id);
                        console.log(`Section ${id} should be shown: ${shouldShow}`);
                        sec.style.display = shouldShow ? 'block' : 'none';
                        toggleRequired(id, shouldShow);

                        // Special handling for wedding-homecoming-section
                        if (id === 'wedding-homecoming-section' && shouldShow) {
                            console.log('Ensuring wedding-homecoming-section is visible');
                            sec.style.display = 'block';

                            // Make sure the dual package selector is required
                            const dualPackageSelect = document.getElementById('dualPackage');
                            if (dualPackageSelect) {
                                dualPackageSelect.required = true;
                                console.log('Set dualPackage to required');
                            }
                        }
                    });
                      // Reset validation styling on change
                     bookingForm.querySelectorAll('[style*="border-color: red"]').forEach(el => el.style.borderColor = '');

                 });

                 // Sync Bride/Groom names - This part seems overly complex and potentially buggy if IDs change.
                 // It's often better to just have one set of name fields shown based on context.
                 // Simplified: The 'coupleNamesSection' fields are the primary source.
                 // syncNames(); // Removed call to avoid potential issues. Use the main couple name fields.

                // Generate booking summary for review
                function generateSummary() {
                    const summaryDiv = document.getElementById('bookingSummary');
                    if (!summaryDiv) return; // Exit if element not found

                    const eventTypeVal = eventTypeSelect.value;
                    const eventTypeText = eventTypeSelect.options[eventTypeSelect.selectedIndex].text;
                    let summaryHtml = `<p><strong>Event Type:</strong> ${eventTypeText}</p>`;
                    const bride = document.getElementById('brideName').value;
                    const groom = document.getElementById('groomName').value;
                    if (bride || groom) {
                         summaryHtml += `<p><strong>Couple:</strong> ${bride || 'N/A'} & ${groom || 'N/A'}</p>`;
                    }

                     // Helper to get value or N/A
                     const getVal = (id) => document.getElementById(id)?.value || 'N/A';
                     const getSelectedText = (id) => {
                         const select = document.getElementById(id);
                         return select ? (select.selectedIndex > 0 ? select.options[select.selectedIndex].text : 'Not Selected') : 'N/A';
                     }

                     summaryHtml += `<hr class="my-3 border-gray-300">`;

                    // Add details based on visible sections
                    if (document.getElementById('preshoot-section').style.display === 'block') {
                        summaryHtml += `<h4>Pre-Shoot Details:</h4>`;
                        summaryHtml += `<p><strong>Package:</strong> ${getSelectedText('preshootPackage')}</p>`;
                        summaryHtml += `<p><strong>Preferred Date:</strong> ${getVal('preshootDate')}</p>`;
                        summaryHtml += `<p><strong>Location/Vibe:</strong> ${getVal('preshootLocation')}</p>`;
                        summaryHtml += `<p><strong>Outfit Changes:</strong> ${getVal('preshootCostumes') || 'Not specified'}</p>`; // Corrected ID
                        // Removed preshootNotes as it doesn't exist, general notes captured later
                    }
                     if (document.getElementById('engagement-section').style.display === 'block') {
                         summaryHtml += `<h4>Engagement Details:</h4>`;
                         summaryHtml += `<p><strong>Package:</strong> ${getSelectedText('engagementPackage')}</p>`;
                         summaryHtml += `<p><strong>Date:</strong> ${getVal('engagementDate')}</p>`;
                         summaryHtml += `<p><strong>Venue/Hotel Name:</strong> ${getVal('engagementVenue')}</p>`;
                         summaryHtml += `<p><strong>Venue Location:</strong> ${getVal('engagementLocation')}</p>`;
                         summaryHtml += `<p><strong>Registration Time:</strong> ${getVal('registrationTime')}</p>`;
                         summaryHtml += `<p><strong>Ring Exchange Time:</strong> ${getVal('ringTime')}</p>`;
                         summaryHtml += `<p><strong>Event End Time:</strong> ${getVal('engagementEndTime')}</p>`;
                         summaryHtml += `<p><strong>Guest Count:</strong> ${getVal('guestCount')}</p>`;
                         summaryHtml += `<p><strong>Makeup Artist/Salon:</strong> ${getVal('brideMakeup') || 'Not specified'}</p>`;
                     }
                     if (document.getElementById('wedding-section').style.display === 'block') {
                         summaryHtml += `<h4>Wedding Details:</h4>`;
                          // Show correct package based on event type
                         if (eventTypeVal === 'Wedding') {
                              summaryHtml += `<p><strong>Package:</strong> ${getSelectedText('weddingPackage')}</p>`;
                         } else if (eventTypeVal === 'Wedding & Homecoming Combo') {
                              summaryHtml += `<p><strong>Package:</strong> ${getSelectedText('dualPackage')}</p>`;
                         } else if (eventTypeVal === 'Pre-shoot, Wedding & Homecoming Combo') {
                              // Combo package already shown under Pre-shoot
                         }
                         summaryHtml += `<p><strong>Date:</strong> ${getVal('weddingDate')}</p>`;
                         summaryHtml += `<p><strong>Venue/Hotel Name:</strong> ${getVal('weddingVenue')}</p>`;
                         // Removed weddingLocation as it doesn't exist, using Venue/Hotel Name instead
                         summaryHtml += `<p><strong>Ceremony (Poruwa) Start Time:</strong> ${getVal('poruwaTime')}</p>`;
                         summaryHtml += `<p><strong>Photography Start Time:</strong> ${getVal('weddingStartTime')}</p>`; // Corrected ID
                         summaryHtml += `<p><strong>Event End Time:</strong> ${getVal('weddingEndTime')}</p>`;
                         summaryHtml += `<p><strong>Guest Count:</strong> ${getVal('weddingGuestCount')}</p>`;
                         summaryHtml += `<p><strong>Makeup Artist/Salon:</strong> ${getVal('weddingBrideMakeup') || 'Not specified'}</p>`; // Corrected ID
                     }
                     if (document.getElementById('homecoming-section').style.display === 'block') {
                         summaryHtml += `<h4>Homecoming Details:</h4>`;
                         // Show correct package based on event type
                         if (eventTypeVal === 'Homecoming') {
                             summaryHtml += `<p><strong>Package:</strong> ${getSelectedText('homecomingPackage')}</p>`;
                         } // Package for combos handled in Wedding section summary
                         summaryHtml += `<p><strong>Date:</strong> ${getVal('homecomingDate')}</p>`;
                         summaryHtml += `<p><strong>Venue/Hotel Name:</strong> ${getVal('homecomingVenue')}</p>`;
                         summaryHtml += `<p><strong>Photoshoot Location:</strong> ${getVal('homecomingLocation')}</p>`; // Corrected ID
                         summaryHtml += `<p><strong>Photography Start Time:</strong> ${getVal('homecomingStartTime')}</p>`;
                         summaryHtml += `<p><strong>Event End Time:</strong> ${getVal('homecomingEndTime')}</p>`;
                         summaryHtml += `<p><strong>Guest Count:</strong> ${getVal('homecomingGuestCount')}</p>`;
                         summaryHtml += `<p><strong>Makeup Artist/Salon:</strong> ${getVal('homecomingBrideMakeup') || 'Not specified'}</p>`; // Corrected ID
                     }

                    summaryHtml += `<hr class="my-3 border-gray-300">`;
                    summaryHtml += `<h4>Contact Info:</h4>`;
                    summaryHtml += `<p><strong>Phone:</strong> ${getVal('phoneNumber')}</p>`;
                    summaryHtml += `<p><strong>Email:</strong> ${getVal('emailAddress')}</p>`;
                    summaryHtml += `<p><strong>Heard Via / General Notes:</strong> ${getVal('additionalContact') || 'N/A'}</p>`; // Added general notes

                    // Add specific notes if they exist
                    const weddingNotes = getVal('weddingSpecialRequests');
                    const engagementNotes = getVal('specialRequests'); // Assuming this is engagement notes
                    const homecomingNotes = getVal('homecomingSpecialRequests');

                    if (weddingNotes && weddingNotes !== 'N/A') {
                        summaryHtml += `<p><strong>Wedding Notes:</strong> ${weddingNotes}</p>`;
                    }
                    if (engagementNotes && engagementNotes !== 'N/A') {
                        summaryHtml += `<p><strong>Engagement Notes:</strong> ${engagementNotes}</p>`;
                    }
                     if (homecomingNotes && homecomingNotes !== 'N/A') {
                        summaryHtml += `<p><strong>Homecoming Notes:</strong> ${homecomingNotes}</p>`;
                    }

                    summaryDiv.innerHTML = summaryHtml;
                }


                 // Helper function to get package price (dummy prices for demonstration)
                function getPackagePrice(packageKey) {
                     // Simplified: Use the actual value from the select dropdown directly as the key
                    const priceMap = {
                        // Engagement
                        'Engagement Package 1': 35000, 'Engagement Package 2': 55000, 'Engagement Package 3': 75000,
                        // Wedding
                        'Wedding Package 1': 60000, 'Wedding Package 2': 80000, 'Wedding Package 3': 100000,
                        'Wedding Package 4': 130000, 'Wedding Package 5': 160000, 'Wedding Package 6': 190000,
                        // Homecoming (Standalone)
                        'Homecoming Package 1': 30000, 'Homecoming Package 2': 50000, 'Homecoming Package 3': 70000,
                        'Homecoming Package 4': 90000, 'Homecoming Package 5': 150000, 'Homecoming Package 6': 200000,
                        // Wedding & Homecoming Combo
                        'Wedding & Homecoming Dual Package 1': 100000, 'Wedding & Homecoming Dual Package 2': 160000, 'Wedding & Homecoming Dual Package 3': 200000,
                        'Dual Package 1': 100000, 'Dual Package 2': 160000, 'Dual Package 3': 200000,
                        // Pre-shoot, Wedding & Homecoming Combo
                        'Pre-shoot, Wedding & Homecoming Combo Package 1': 150000, 'Pre-shoot, Wedding & Homecoming Combo Package 2': 200000, 'Pre-shoot, Wedding & Homecoming Combo Package 3': 270000,
                        'Combo Package 1': 150000, 'Combo Package 2': 200000, 'Combo Package 3': 270000,
                        // Default / Not selected
                         '': 0
                    };
                     return priceMap[packageKey] !== undefined ? priceMap[packageKey] : 0; // Return 0 if key not found
                }


                // Generate invoice details (Now Summary details)
                function generateInvoiceSummary() {
                     const eventTypeValue = eventTypeSelect.value;
                     console.log('Event Type Value:', eventTypeValue);
                     console.log('dualPackage value:', document.getElementById('dualPackage')?.value);

                     const currentDate = new Date();
                     const invoiceNumber = 'INQ-' + Math.floor(10000 + Math.random() * 90000); // 5 digit random
                     const invoiceDate = currentDate.toLocaleDateString('en-CA'); // Use YYYY-MM-DD for clarity

                     document.getElementById('invoiceNumber').textContent = invoiceNumber;
                     document.getElementById('invoiceDate').textContent = invoiceDate;

                     const clientName = (document.getElementById('brideName').value || 'Client') + (document.getElementById('groomName').value ? ' & ' + document.getElementById('groomName').value : '');
                     document.getElementById('invoiceClientName').textContent = clientName;
                     document.getElementById('invoiceClientEmail').textContent = document.getElementById('emailAddress').value || 'N/A';
                     document.getElementById('invoiceClientPhone').textContent = document.getElementById('phoneNumber').value || 'N/A';

                     const itemsTbody = document.getElementById('invoiceItems');
                     itemsTbody.innerHTML = ''; // Clear previous items
                     let total = 0;
                     let description = eventTypeSelect.options[eventTypeSelect.selectedIndex].text;
                     let eventDateInfo = '';
                     let packageName = '';
                     let packagePrice = 0;

                      // Determine package name and price based on event type
                      if (eventTypeValue === 'Engagement') {
                         packageName = document.getElementById('engagementPackage').value;
                         eventDateInfo = document.getElementById('engagementDate').value || 'TBC';
                         packagePrice = getPackagePrice(packageName);
                     } else if (eventTypeValue === 'Wedding') {
                          packageName = document.getElementById('weddingPackage').value;
                          eventDateInfo = document.getElementById('weddingDate').value || 'TBC';
                          packagePrice = getPackagePrice(packageName);
                     } else if (eventTypeValue === 'Homecoming') {
                          packageName = document.getElementById('homecomingPackage').value;
                          eventDateInfo = document.getElementById('homecomingDate').value || 'TBC';
                          packagePrice = getPackagePrice(packageName);
                     } else if (eventTypeValue === 'Wedding & Homecoming Combo') {
                          packageName = document.getElementById('dualPackage').value;
                          const wedDate = document.getElementById('weddingDate').value || 'TBC';
                          const hcDate = document.getElementById('homecomingDate').value || 'TBC';
                          eventDateInfo = `Wed: ${wedDate}, HC: ${hcDate}`;
                          packagePrice = getPackagePrice(packageName);
                     } else if (eventTypeValue === 'Pre-shoot, Wedding & Homecoming Combo') {
                         packageName = document.getElementById('preshootPackage').value;
                         const psDate = document.getElementById('preshootDate').value || 'TBC';
                         const wedDate = document.getElementById('weddingDate').value || 'TBC';
                         const hcDate = document.getElementById('homecomingDate').value || 'TBC';
                         eventDateInfo = `PS: ${psDate}, Wed: ${wedDate}, HC: ${hcDate}`;
                         packagePrice = getPackagePrice(packageName);
                     }

                     total += packagePrice;

                     // Format price for display
                     const formattedPrice = packagePrice > 0 ? `~ ${packagePrice.toLocaleString()}` : 'Quote Pending';
                     const formattedTotal = total > 0 ? `~ ${total.toLocaleString()}` : 'Quote Pending';


                     itemsTbody.innerHTML = `
                         <tr>
                             <td>${description}</td>
                             <td>${eventDateInfo}</td>
                             <td>${packageName || 'N/A'}</td>
                             <td class="text-right">${formattedPrice}</td>
                         </tr>
                     `;

                      document.getElementById('invoiceTotal').textContent = formattedTotal;
                 }


                // Handle Booking Form Submission
                 bookingForm.addEventListener('submit', function(e) {
                     e.preventDefault();
                     if (!validateStep(4)) { // Validate the final step (includes terms checkbox)
                         return;
                     }

                     // Hide booking section, show invoice section
                     document.getElementById('booking').style.display = 'none';
                     generateInvoiceSummary(); // Generate the summary view
                     document.getElementById('invoice').style.display = 'block';

                     // Scroll to the invoice section
                     document.getElementById('invoice').scrollIntoView({ behavior: 'smooth' });

                     // Form data will be submitted directly here instead of triggering another event
                     console.log('Booking form completed. Submitting data to server...');

                     // Collect form data
                     const formData = {
                       eventType: document.getElementById('eventType')?.value,
                       eventDate: document.getElementById('eventDate')?.value || document.getElementById('weddingDate')?.value || document.getElementById('engagementDate')?.value || document.getElementById('homecomingDate')?.value,
                       selectedPackage: document.getElementById('engagementPackage')?.value || document.getElementById('weddingPackage')?.value || document.getElementById('homecomingPackage')?.value || document.getElementById('dualPackage')?.value || document.getElementById('preshootPackage')?.value,
                       clientName: `${document.getElementById('brideName')?.value || ''} & ${document.getElementById('groomName')?.value || ''}`,
                       clientEmail: document.getElementById('emailAddress')?.value,
                       clientPhone: document.getElementById('phoneNumber')?.value,
                       brideName: document.getElementById('brideName')?.value || undefined,
                       groomName: document.getElementById('groomName')?.value || undefined,
                       eventLocation: document.getElementById('engagementLocation')?.value || document.getElementById('weddingLocation')?.value || document.getElementById('homecomingLocation')?.value,
                       additionalNotes: document.getElementById('specialRequests')?.value || undefined,
                       pdfDataUri: document.getElementById('pdf-data-uri')?.value || '',
                       // Add event-specific fields
                       venueHotelName: document.getElementById('engagementVenue')?.value || document.getElementById('weddingVenue')?.value || document.getElementById('homecomingVenue')?.value,

                       // Engagement-specific fields
                       keyMomentTime: document.getElementById('registrationTime')?.value,
                       ringExchangeTime: document.getElementById('ringTime')?.value,

                       // Wedding-specific fields
                       primaryPhotoLocation: document.getElementById('weddingPhotoshootLocation')?.value,
                       ceremonyStartTime: document.getElementById('poruwaTime')?.value,
                       photographyStartTime: document.getElementById('weddingStartTime')?.value,

                       // Common fields
                       eventEndTime: document.getElementById('engagementEndTime')?.value || document.getElementById('weddingEndTime')?.value || document.getElementById('homecomingEndTime')?.value,
                       guestCount: document.getElementById('guestCount')?.value || document.getElementById('weddingGuestCount')?.value || document.getElementById('homecomingGuestCount')?.value,
                       makeupArtistSalon: document.getElementById('brideMakeup')?.value || document.getElementById('weddingBrideMakeup')?.value || document.getElementById('homecomingMakeup')?.value,

                       // Pre-shoot specific fields
                       preShootDate: document.getElementById('preshootDate')?.value,
                       preShootLocation: document.getElementById('preshootLocation')?.value,
                       outfitChanges: document.getElementById('outfitChanges')?.value,

                       // Homecoming specific fields
                       homecomingDate: document.getElementById('homecomingDate')?.value,
                       homecomingVenue: document.getElementById('homecomingVenue')?.value,
                       homecomingPhotoLocation: document.getElementById('homecomingLocation')?.value,
                       homecomingStartTime: document.getElementById('homecomingStartTime')?.value,
                       homecomingEndTime: document.getElementById('homecomingEndTime')?.value,
                       homecomingGuestCount: document.getElementById('homecomingGuestCount')?.value
                     };

                     // Show loading indicator
                     const formMessage = document.getElementById('form-message');
                     const loadingIndicator = document.getElementById('loading-indicator');
                     formMessage.textContent = '';
                     formMessage.className = 'mt-6 text-center font-medium';
                     loadingIndicator.classList.remove('hidden');

                     // Submit the data
                     fetch(`${window.location.protocol === 'file:' ? 'http://localhost:5000' : ''}/api/bookings`, {
                       method: 'POST',
                       headers: {
                         'Content-Type': 'application/json',
                       },
                       body: JSON.stringify({
                         // Basic Information
                         eventType: document.getElementById('eventType').value,
                         eventDate: document.getElementById('weddingDate')?.value || document.getElementById('engagementDate')?.value || document.getElementById('homecomingDate')?.value,
                         selectedPackage: document.getElementById('weddingPackage')?.value || document.getElementById('engagementPackage')?.value || document.getElementById('homecomingPackage')?.value || document.getElementById('dualPackage')?.value || document.getElementById('preshootPackage')?.value,
                         clientName: document.getElementById('brideName').value + ' & ' + document.getElementById('groomName').value,
                         clientEmail: document.getElementById('emailAddress').value,
                         clientPhone: document.getElementById('phoneNumber').value,
                         brideName: document.getElementById('brideName').value,
                         groomName: document.getElementById('groomName').value,

                         // Location Information
                         eventLocation: document.getElementById('weddingLocation')?.value || document.getElementById('engagementLocation')?.value || document.getElementById('homecomingLocation')?.value,
                         venueHotelName: document.getElementById('engagementVenue')?.value || document.getElementById('weddingVenue')?.value || document.getElementById('homecomingVenue')?.value,

                         // Engagement-specific fields
                         keyMomentTime: document.getElementById('registrationTime')?.value,
                         ringExchangeTime: document.getElementById('ringTime')?.value,

                         // Wedding-specific fields
                         primaryPhotoLocation: document.getElementById('weddingPhotoshootLocation')?.value,
                         ceremonyStartTime: document.getElementById('poruwaTime')?.value,
                         photographyStartTime: document.getElementById('weddingStartTime')?.value,

                         // Common fields
                         eventEndTime: document.getElementById('engagementEndTime')?.value || document.getElementById('weddingEndTime')?.value || document.getElementById('homecomingEndTime')?.value,
                         guestCount: document.getElementById('guestCount')?.value || document.getElementById('weddingGuestCount')?.value || document.getElementById('homecomingGuestCount')?.value,
                         makeupArtistSalon: document.getElementById('brideMakeup')?.value || document.getElementById('weddingBrideMakeup')?.value || document.getElementById('homecomingMakeup')?.value,

                         // Pre-shoot specific fields
                         preShootDate: document.getElementById('preshootDate')?.value,
                         preShootLocation: document.getElementById('preshootLocation')?.value,
                         outfitChanges: document.getElementById('outfitChanges')?.value,

                         // Homecoming specific fields
                         homecomingDate: document.getElementById('homecomingDate')?.value,
                         homecomingVenue: document.getElementById('homecomingVenue')?.value,
                         homecomingPhotoLocation: document.getElementById('homecomingLocation')?.value,
                         homecomingStartTime: document.getElementById('homecomingStartTime')?.value,
                         homecomingEndTime: document.getElementById('homecomingEndTime')?.value,
                         homecomingGuestCount: document.getElementById('homecomingGuestCount')?.value,

                         // Additional Information
                         additionalNotes: document.getElementById('weddingSpecialRequests')?.value || document.getElementById('specialRequests')?.value || document.getElementById('homecomingSpecialRequests')?.value
                       })
                     })
                     .then(async response => {
                       if (!response.ok) {
                         const errorData = await response.json();
                         throw new Error(errorData.message || 'Failed to submit booking');
                       }
                       return response.json();
                     })
                     .then(data => {
                       loadingIndicator.classList.add('hidden');
                       formMessage.textContent = data.message || 'Booking submitted successfully! Please check your email.';
                       formMessage.classList.add('text-green-600');
                       console.log('Booking request sent successfully:', data);
                       bookingForm.reset();

                       // Show the invoice section
                       document.getElementById('invoice').style.display = 'block';
                       document.getElementById('invoice').scrollIntoView({ behavior: 'smooth' });
                     })
                     .catch(error => {
                       loadingIndicator.classList.add('hidden');
                       console.error('Network or fetch error:', error);
                       formMessage.textContent = 'An error occurred while submitting the form. Please check your connection and try again.';
                       formMessage.classList.add('text-red-600');
                     })
                     .then(response => {
                       // When using no-cors mode, we can't access the response content
                       // So we'll just assume success if we get a response
                       loadingIndicator.classList.add('hidden');
                       formMessage.textContent = 'Booking submitted successfully! Please check your email.';
                       formMessage.classList.add('text-green-600');
                       console.log('Booking request sent successfully');
                       bookingForm.reset();

                       // Show the invoice section
                       document.getElementById('invoice').style.display = 'block';
                       document.getElementById('invoice').scrollIntoView({ behavior: 'smooth' });

                       return { success: true };
                     })
                     .catch(error => {
                       loadingIndicator.classList.add('hidden');
                       console.error('Network or fetch error:', error);
                       formMessage.textContent = 'An error occurred while submitting the form. Please check your connection and try again.';
                       formMessage.classList.add('text-red-600');
                     });
                 });

                 // Return to home from invoice (summary)
                 document.getElementById('returnToHome').addEventListener('click', function() {
                     document.getElementById('invoice').style.display = 'none';
                      // Show booking section again, maybe reset? Or just scroll up.
                      document.getElementById('booking').style.display = 'block'; // Re-show booking section
                      showFormSection(1); // Go back to step 1
                      bookingForm.reset(); // Reset the form fields
                      eventTypeSelect.dispatchEvent(new Event('change')); // Trigger change to reset dynamic sections
                      window.scrollTo({ top: document.getElementById('booking').offsetTop - 100, behavior: 'smooth' }); // Scroll back to booking section
                 });

                 // Download Invoice (Summary) using jsPDF and html2canvas
                 const downloadBtn = document.getElementById('downloadInvoice');
                 if (downloadBtn) {
                     downloadBtn.addEventListener('click', function() {
                          const { jsPDF } = window.jspdf;
                          const invoiceElement = document.querySelector('#invoice .invoice'); // Target the specific invoice div
                          if (!invoiceElement) {
                              console.error("Invoice element not found for download.");
                              return;
                          }
                          const invoiceNumber = document.getElementById('invoiceNumber').textContent || 'inquiry-summary';
                          const downloadButton = this;

                          // Temporarily disable button and show loading state
                          downloadButton.disabled = true;
                          downloadButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';

                          html2canvas(invoiceElement, {
                              scale: 2,
                              useCORS: true,
                              logging: false // Reduce console noise
                          }).then(canvas => {
                              const imgData = canvas.toDataURL('image/png');
                              const pdf = new jsPDF('p', 'mm', 'a4');
                              const pdfWidth = pdf.internal.pageSize.getWidth();
                              const pdfHeight = pdf.internal.pageSize.getHeight();
                              const imgProps = pdf.getImageProperties(imgData);
                              const imgWidth = imgProps.width;
                              const imgHeight = imgProps.height;

                               // Calculate aspect ratio and dimensions
                              const ratio = Math.min((pdfWidth - 20) / imgWidth, (pdfHeight - 20) / imgHeight); // Use margins (10mm each side)
                              const finalWidth = imgWidth * ratio;
                              const finalHeight = imgHeight * ratio;
                              const imgX = (pdfWidth - finalWidth) / 2; // Center horizontally
                              const imgY = 10; // Top margin

                              pdf.addImage(imgData, 'PNG', imgX, imgY, finalWidth, finalHeight);
                              pdf.save(`inquiry-summary-${invoiceNumber}.pdf`);

                          }).catch(error => {
                              console.error("Error generating PDF:", error);
                              alert("Sorry, there was an error generating the PDF summary. Please try again.");
                          }).finally(() => {
                               // Re-enable button and restore text regardless of success/failure
                              downloadButton.disabled = false;
                              downloadButton.innerHTML = '<i class="fas fa-download mr-2"></i>Save Inquiry Summary';
                          });
                      });
                 }

            } // End if(bookingForm) check


            // --- Contact Form Submission ---
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                 contactForm.addEventListener('submit', function(e) {
                     e.preventDefault();

                     // Basic validation for contact form
                     let contactIsValid = true;
                     contactForm.querySelectorAll('[required]').forEach(input => {
                         input.style.borderColor = ''; // Reset border
                         if (!input.value) {
                             contactIsValid = false;
                             input.style.borderColor = 'red';
                         }
                     });

                     if (!contactIsValid) {
                         alert('Please fill in all required fields.');
                         return;
                     }

                     try {
                           const contactFormData = new FormData(contactForm);
                           const contactData = Object.fromEntries(contactFormData.entries());
                           console.log("Contact Form Data:", contactData);

                           // Create mailto link with form data
                           const subject = encodeURIComponent(contactData.subject || 'Contact Form Inquiry');
                           const body = encodeURIComponent(`Name: ${contactData.name}\nEmail: ${contactData.email}\n\nMessage: ${contactData.message}`);
                           const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

                           // Open email client
                           window.location.href = mailtoLink;

                           // Show success message
                           alert('Thank you for your message. Your email client will open to send the message directly.');
                           contactForm.reset();
                      } catch (error) {
                          console.error("Error processing contact form data:", error);
                          alert('An error occurred. Please try again or contact us <NAME_EMAIL>');
                      }


                     contactForm.reset(); // Clear the form
                 });
             }


        }); // End DOMContentLoaded
    </script>

<script>
  // Frontend Booking Form Submission Handler
  const bookingForm = document.getElementById('booking-form');
  const formMessage = document.getElementById('form-message');
  const loadingIndicator = document.getElementById('loading-indicator');
  const submitButton = document.querySelector('#booking-form button[type="submit"]'); // Get the submit button

  // Define Backend API URL
  const API_URL = '/api/bookings'; // Using relative path for production

  if (bookingForm) {
    bookingForm.addEventListener('submit', async (event) => {
      event.preventDefault(); // Prevent default page reload

      // Clear previous messages and show loading
      formMessage.textContent = '';
      formMessage.className = 'mt-6 text-center font-medium'; // Reset classes
      loadingIndicator.classList.remove('hidden');
      if(submitButton) submitButton.disabled = true;

      // --- Gather Form Data ---
      // Ensure IDs match your actual form elements
      const formData = {
        // Basic information
        eventType: document.getElementById('event-type')?.value,
        eventDate: document.getElementById('event-date')?.value,
        selectedPackage: document.getElementById('package-selection')?.value,
        clientName: document.getElementById('client-name')?.value,
        clientEmail: document.getElementById('client-email')?.value,
        clientPhone: document.getElementById('client-phone')?.value,
        brideName: document.getElementById('bride-name')?.value || undefined,
        groomName: document.getElementById('groom-name')?.value || undefined,
        eventLocation: document.getElementById('event-location')?.value || undefined,
        additionalNotes: document.getElementById('additional-notes')?.value || undefined,

        // We'll collect the actual field values in the mapping section below
        // These are just placeholders
        venueHotelName: undefined,
        primaryPhotoLocation: undefined,
        ceremonyStartTime: undefined,
        photographyStartTime: undefined,
        keyMomentTime: undefined,
        ringExchangeTime: undefined,
        eventEndTime: undefined,
        guestCount: undefined,
        makeupArtistSalon: undefined,

        // Engagement-specific fields
        // These might overlap with common fields, but including for clarity
        registrationTime: document.getElementById('registrationTime')?.value || undefined,
        ringTime: document.getElementById('ringTime')?.value || undefined,
        engagementEndTime: document.getElementById('engagementEndTime')?.value || undefined,
        engagementVenue: document.getElementById('engagementVenue')?.value || undefined,
        engagementLocation: document.getElementById('engagementLocation')?.value || undefined,

        // Wedding-specific fields
        // These might overlap with common fields, but including for clarity
        weddingVenue: document.getElementById('weddingVenue')?.value || undefined,
        weddingLocation: document.getElementById('weddingLocation')?.value || undefined,
        poruwaTime: document.getElementById('poruwaTime')?.value || undefined,
        weddingPhotographyTime: document.getElementById('weddingPhotographyTime')?.value || undefined,
        weddingEndTime: document.getElementById('weddingEndTime')?.value || undefined,
        weddingGuestCount: document.getElementById('weddingGuestCount')?.value || undefined,
        brideMakeup: document.getElementById('brideMakeup')?.value || undefined,

        // Homecoming-specific fields
        homecomingDate: document.getElementById('homecomingDate')?.value || undefined,
        homecomingVenue: document.getElementById('homecomingVenue')?.value || undefined,
        homecomingPhotoLocation: document.getElementById('homecomingPhotoLocation')?.value || undefined,
        homecomingStartTime: document.getElementById('homecomingStartTime')?.value || undefined,
        homecomingEndTime: document.getElementById('homecomingEndTime')?.value || undefined,
        homecomingGuestCount: document.getElementById('homecomingGuestCount')?.value || undefined,

        // Pre-shoot-specific fields
        preShootDate: document.getElementById('preshootDate')?.value || undefined,
        preShootLocation: document.getElementById('preshootLocation')?.value || undefined,
        outfitChanges: document.getElementById('preshootCostumes')?.value || undefined,

        // PDF Data URI
        pdfDataUri: document.getElementById('pdf-data-uri')?.value || '',
      };

      // Map form field IDs to backend field names based on event type
      const eventType = formData.eventType;

      // Map engagement-specific fields
      if (eventType === 'Engagement') {
        console.log('Mapping Engagement fields');
        // Log the values of each field
        console.log('engagementVenue:', document.getElementById('engagementVenue')?.value);
        console.log('engagementLocation:', document.getElementById('engagementLocation')?.value);
        console.log('registrationTime:', document.getElementById('registrationTime')?.value);
        console.log('ringTime:', document.getElementById('ringTime')?.value);
        console.log('engagementEndTime:', document.getElementById('engagementEndTime')?.value);
        console.log('guestCount:', document.getElementById('guestCount')?.value);
        console.log('brideMakeup:', document.getElementById('brideMakeup')?.value);

        // Get values directly from the form elements
        const engagementVenue = document.getElementById('engagementVenue')?.value;
        const engagementLocation = document.getElementById('engagementLocation')?.value;
        const registrationTime = document.getElementById('registrationTime')?.value;
        const ringTime = document.getElementById('ringTime')?.value;
        const engagementEndTime = document.getElementById('engagementEndTime')?.value;
        const guestCount = document.getElementById('guestCount')?.value;
        const brideMakeup = document.getElementById('brideMakeup')?.value;

        console.log('Engagement form values:');
        console.log('engagementVenue:', engagementVenue);
        console.log('engagementLocation:', engagementLocation);
        console.log('registrationTime:', registrationTime);
        console.log('ringTime:', ringTime);
        console.log('engagementEndTime:', engagementEndTime);
        console.log('guestCount:', guestCount);
        console.log('brideMakeup:', brideMakeup);

        formData.venueHotelName = engagementVenue || formData.venueHotelName;
        formData.eventLocation = engagementLocation || formData.eventLocation;
        formData.keyMomentTime = registrationTime || formData.keyMomentTime;
        formData.ringExchangeTime = ringTime || formData.ringExchangeTime;
        formData.eventEndTime = engagementEndTime || formData.eventEndTime;
        formData.guestCount = guestCount || formData.guestCount;
        formData.makeupArtistSalon = brideMakeup || formData.makeupArtistSalon;
      }

      // Map wedding-specific fields
      if (eventType === 'Wedding' || eventType === 'Wedding & Homecoming Combo' || eventType === 'Pre-shoot, Wedding & Homecoming Combo') {
        // Get values directly from the form elements
        formData.venueHotelName = document.getElementById('weddingVenue')?.value || formData.venueHotelName;
        formData.eventLocation = document.getElementById('weddingLocation')?.value || formData.eventLocation;
        formData.ceremonyStartTime = document.getElementById('poruwaTime')?.value || formData.ceremonyStartTime;
        formData.photographyStartTime = document.getElementById('weddingPhotographyTime')?.value || formData.photographyStartTime;
        formData.eventEndTime = document.getElementById('weddingEndTime')?.value || formData.eventEndTime;
        formData.guestCount = document.getElementById('weddingGuestCount')?.value || formData.guestCount;
        formData.makeupArtistSalon = document.getElementById('brideMakeup')?.value || formData.makeupArtistSalon;
      }

      // Map pre-shoot fields
      if (eventType === 'Pre-shoot, Wedding & Homecoming Combo') {
        // Get values directly from the form elements
        formData.preShootDate = document.getElementById('preshootDate')?.value || formData.preShootDate;
        formData.preShootLocation = document.getElementById('preshootLocation')?.value || formData.preShootLocation;
        formData.outfitChanges = document.getElementById('preshootCostumes')?.value || formData.outfitChanges;
      }

      // Map homecoming fields
      if (eventType === 'Homecoming' || eventType === 'Wedding & Homecoming Combo' || eventType === 'Pre-shoot, Wedding & Homecoming Combo') {
        // Get values directly from the form elements
        formData.homecomingDate = document.getElementById('homecomingDate')?.value || formData.homecomingDate;
        formData.homecomingVenue = document.getElementById('homecomingVenue')?.value || formData.homecomingVenue;
        formData.homecomingPhotoLocation = document.getElementById('homecomingPhotoLocation')?.value || formData.homecomingPhotoLocation;
        formData.homecomingStartTime = document.getElementById('homecomingStartTime')?.value || formData.homecomingStartTime;
        formData.homecomingEndTime = document.getElementById('homecomingEndTime')?.value || formData.homecomingEndTime;
        formData.homecomingGuestCount = document.getElementById('homecomingGuestCount')?.value || formData.homecomingGuestCount;
      }

      console.log('Submitting booking data (PDF URI truncated):', {...formData, pdfDataUri: formData.pdfDataUri ? formData.pdfDataUri.substring(0, 50) + '...' : 'N/A'});

      // Log the final values of the event-specific fields
      console.log('Final venueHotelName:', formData.venueHotelName);
      console.log('Final eventLocation:', formData.eventLocation);
      console.log('Final keyMomentTime:', formData.keyMomentTime);
      console.log('Final ringExchangeTime:', formData.ringExchangeTime);
      console.log('Final eventEndTime:', formData.eventEndTime);
      console.log('Final guestCount:', formData.guestCount);
      console.log('Final makeupArtistSalon:', formData.makeupArtistSalon);

      // Basic check if PDF data is present (optional, backend should also validate)
      if (!formData.pdfDataUri) {
          console.warn('PDF Data URI is missing. Was the summary generated?');
          // You might want to alert the user or prevent submission here
          // formMessage.textContent = 'Please generate the PDF summary before submitting.';
          // formMessage.classList.add('text-red-600');
          // loadingIndicator.classList.add('hidden');
          // if(submitButton) submitButton.disabled = false;
          // return; // Stop submission if PDF is mandatory
      }

      try {
        console.log('Sending booking data to server:', API_URL);
        console.log('Form data being sent:', formData);

        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        console.log('Server response status:', response.status);
        console.log('Server response headers:', response.headers);

        const result = await response.json(); // Always try to parse JSON
        console.log('Server response data:', result);

        if (response.ok && response.status === 201) {
          // Success
          formMessage.textContent = result.message || 'Booking submitted successfully! Please check your email.';
          formMessage.classList.add('text-green-600');
          console.log('Booking successful:', result);
          bookingForm.reset(); // Clear the form
          // Optionally, redirect or show a persistent success message
          // You might want to hide the form steps and only show the success message
          // e.g., document.getElementById('step-4').style.display = 'none';
          //      document.getElementById('step-1').style.display = 'none'; etc.
        } else {
          // Handle backend errors (validation, server issues)
          formMessage.textContent = result.message || `Error: ${response.statusText} (Status: ${response.status})`;
          formMessage.classList.add('text-red-600');
          console.error('Booking submission error:', result);
        }

      } catch (error) {
        // Handle network errors or other fetch issues
        console.error('Network or fetch error:', error);
        formMessage.textContent = 'An error occurred while submitting the form. Please check your connection and try again.';
        formMessage.classList.add('text-red-600');
      } finally {
        // Hide loading indicator and re-enable button regardless of outcome
        loadingIndicator.classList.add('hidden');
        if(submitButton) submitButton.disabled = false;
      }
    });
  } else {
    console.warn('Booking form with id "booking-form" not found.');
  }

  // Add-on: Ensure date input prevents past dates (basic client-side validation)
  const dateInput = document.getElementById('event-date');
  if(dateInput) {
      const today = new Date().toISOString().split('T')[0];
      dateInput.setAttribute('min', today);
  }

</script>

</body>
</html>